# Medo API Documentation

## API Documentation

For detailed and up-to-date API documentation, please refer to our Swagger UI:

[Swagger Documentation](https://medo-api-14d91def7e7a.herokuapp.com/docs#/)

This documentation is automatically generated and always reflects the latest API changes.

## Authentication

The API uses Firebase authentication. To interact with the API from a front-end application (mobile or web), follow these steps:

1. Authenticate the user using Firebase.
2. Obtain the ID token according to the client type. Refer to the [Firebase documentation](https://firebase.google.com/docs/auth/admin/verify-id-tokens#retrieve_id_tokens_on_clients) for guidance.
3. Use the ID token in the `Authorization` header in the format `Bearer <id token>` to access the API.

## API Endpoints

For a comprehensive list of API endpoints and their descriptions, please refer to the Swagger documentation linked above.

## Sense Score Calculation

The sense score is calculated based on the user's initial pillars in the following areas:

- **Sleep** (0-20 points)
- **Exercise** (0-50 points)
- **Nutrition** (0-45 points)
- **Social Connection** (0-35 points)
- **Emotional Well-being** (0-25 points)

The score represents an overall measure of the user's well-being, and the API provides a delta to show changes over time.

## Recommendations

Recommendations are generated based on the user's health data, including their initial pillars, recent check-ins, and sense score. These recommendations provide actionable suggestions for improving the user's health and wellness.

## Development

### Environment Setup

Ensure that the following environment variables are configured:

- `FIREBASE_PROJECT_ID`
- `FIREBASE_PRIVATE_KEY`
- `FIREBASE_PRIVATE_KEY_ID`
- `FIREBASE_CLIENT_EMAIL`
- `FIREBASE_CLIENT_ID`
- `FIREBASE_CLIENT_x509_CERT_URL`
- `OPENAI_API_KEY`
- `ANTHROPIC_API_KEY`
- `TYPEFORM_API_KEY`
- `DATABASE_URL`
- `BINARY_DATA_NAME_TO_USE`
- `LANGSMITH_TRACING`
- `LLM_MODEL`

### Choosing the LLM Model

- The LLM model is chosen based on the `LLM_MODEL` environment variable. The default is `claude-3-haiku-20240307`.
- OpenAI and Anthropic models are supported.

- These are the models supported and can be defined in the `.env` file:
  - `claude-3-5-sonnet-20241022` - also `claude-3-5-sonnet-latest`
  - `claude-3-5-sonnet-20240620`
  - `claude-3-sonnet-20240229`
  - `claude-3-haiku-20240307`
  - `gpt-4o`
  - `gpt-4o-mini`



## Time-Aware Responses

To provide more contextual and time-sensitive responses, the API now supports time-aware functionality. This is particularly useful for questions or advice related to daily routines, meal times, or sleep schedules.

### Usage

When making a request to the `/ask_question` and `/recommendations` endpoint, you can include an optional `utc_offset` header to indicate the user's local time zone.

#### Header

- `utc_offset`: A float representing the hours offset from UTC (e.g., -5.0 for EST, 5.5 for IST)

#### Example Request

```bash
curl -X POST "https://your-api-url/ask_question" \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer <id_token>" \
     -d '{"question": "What's a good breakfast option for me right now?", "utc_offset": -5.0}'
```


#### Behavior

1. If the `utc_offset` is provided:
   - The AI will consider the user's local time when generating responses.
   - Time-specific advice (e.g., meal suggestions, sleep recommendations) will be tailored to the user's current time of day.

2. If the `utc_offset` is not provided:
   - The AI will generate responses without specific time context.
   - General advice will be given without reference to the time of day.

### Implementation Notes

- The `utc_offset` is used to calculate the user's current local time.
- The current time is only used for context in the AI's response and is not stored with the chat history, which is stored in UTC.


### Running the Application

The project uses Poetry for dependency management and Poe the Poet for task automation.

1. **Install dependencies:**
   ```bash
   poetry install
   ```

2. **Run database migrations:**
   ```bash
   poetry run poe alembic-upgrade-head
   ```

3. **Start the development server:**
   ```bash
   poetry run poe start-dev
   ```

### Linting and Formatting

To lint and format the code, use the following tasks:

- **Lint the code:**
  ```bash
  poetry run poe lint
  ```

- **Format the code:**
  ```bash
  poetry run poe format
  ```

### Testing

Tests are divided into unit and integration tests. To run all tests, use:

```bash
poetry run poe test
```

- **Run unit tests:**
  ```bash
  poetry run poe test-unit
  ```

- **Run integration tests:**
  ```bash
  poetry run poe test-integration
  ```

### Deployment

The API is deployed on Heroku. Ensure all required environment variables are set in the Heroku dashboard or using the Heroku CLI.

## Project Dependencies

This project uses Poetry for dependency management. Key dependencies include:

- FastAPI
- SQLAlchemy
- Alembic
- Firebase Admin
- OpenAI
- Anthropic
- Pytest

For a complete list of dependencies and their versions, please refer to the `pyproject.toml` file in the project root.

## Contributing

Please refer to our contribution guidelines (if available) for information on how to contribute to this project.

## License

[Include license information if applicable]
