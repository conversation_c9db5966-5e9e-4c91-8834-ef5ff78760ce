#!/bin/bash

# Medo API Development Server Script
# This script helps you manage the local development environment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}=== $1 ===${NC}"
}

# Set environment variables
setup_env() {
    export PATH="$HOME/Library/Python/3.9/bin:$PATH"
    export DATABASE_URL="postgresql://postgres_user:232hb1@localhost:5461/medo-api-dev"
    export REDISCLOUD_URL="redis://localhost:6399"
    export PORT=8000
    export OPENAI_API_KEY="sk-placeholder-key-for-development"
    export ANTHROPIC_API_KEY="sk-ant-placeholder-key-for-development"
    export TYPEFORM_API_KEY="tfp_placeholder_key_for_development"
    export LANGCHAIN_API_KEY="lsv2_placeholder_key_for_development"
    export FIREBASE_PROJECT_ID="medo-dev-project"
    export FIREBASE_PRIVATE_KEY_ID="placeholder_key_id"
    export FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nplaceholder_private_key_content\n-----END PRIVATE KEY-----\n"
    export FIREBASE_CLIENT_EMAIL="<EMAIL>"
    export FIREBASE_CLIENT_ID="placeholder_client_id"
    export FIREBASE_CLIENT_x509_CERT_URL="https://www.googleapis.com/robot/v1/metadata/x509/firebase-adminsdk%40medo-dev-project.iam.gserviceaccount.com"
    export BINARY_DATA_NAME_TO_USE="development_binary_data"
    export LLM_MODEL="claude-3-haiku-********"
    export LANGSMITH_TRACING="false"
}

# Check if Docker services are running
check_services() {
    print_header "Checking Services"
    
    if ! docker-compose -f docker-compose.dev.yaml ps | grep -q "Up"; then
        print_warning "Docker services are not running. Starting them..."
        docker-compose -f docker-compose.dev.yaml up -d
        sleep 5
    else
        print_status "Docker services are running"
    fi
}

# Run tests
run_tests() {
    print_header "Running Environment Tests"
    setup_env
    poetry run python test_setup.py
}

# Start the development server
start_server() {
    print_header "Starting Development Server"
    setup_env
    print_status "Server will be available at: http://localhost:8000"
    print_status "API Documentation: http://localhost:8000/docs"
    print_status "Alternative docs: http://localhost:8000/redoc"
    print_status "Press Ctrl+C to stop the server"
    echo
    poetry run uvicorn medo_api.main:app --port $PORT --reload
}

# Stop services
stop_services() {
    print_header "Stopping Services"
    docker-compose -f docker-compose.dev.yaml down
    print_status "Services stopped"
}

# Show help
show_help() {
    echo "Medo API Development Server Management Script"
    echo
    echo "Usage: $0 [COMMAND]"
    echo
    echo "Commands:"
    echo "  start     Start the development server (default)"
    echo "  test      Run environment tests"
    echo "  services  Start Docker services only"
    echo "  stop      Stop all services"
    echo "  help      Show this help message"
    echo
    echo "Examples:"
    echo "  $0 start    # Start the development server"
    echo "  $0 test     # Test the environment setup"
    echo "  $0 stop     # Stop all services"
}

# Main script logic
case "${1:-start}" in
    "start")
        check_services
        start_server
        ;;
    "test")
        check_services
        run_tests
        ;;
    "services")
        check_services
        ;;
    "stop")
        stop_services
        ;;
    "help"|"-h"|"--help")
        show_help
        ;;
    *)
        print_error "Unknown command: $1"
        show_help
        exit 1
        ;;
esac
