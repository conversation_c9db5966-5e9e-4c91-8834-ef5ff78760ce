# medo_api/ai/meal_analysis.py

import logging
import os
from datetime import datetime
from typing import Any, Dict

from langchain_core.output_parsers import JsonOutputParser
from pydantic import BaseModel, Field
from sqlalchemy import func, select
from sqlalchemy.orm import Session

from ..db.models import BinaryData, MealLog, User
from ..rag_lib import RAGProcessor
from ..schemas.meal_logs import MealLog as MealLogSchema

BINARY_DATA_NAME = os.environ["BINARY_DATA_NAME_TO_USE"]

logger = logging.getLogger(__name__)


class MealAnalysisException(Exception):
    pass


class MealAnalysisResult(BaseModel):
    analysis: Dict[str, Any]  # Change this to accept a dictionary


class MealAnalysisOutput(BaseModel):
    score: int = Field(description="Score for the meal on a scale of 1-5")
    summary: str = Field(
        description="Summary of the meal if it's not the last meal of the day or daily meals"  # noqa: E501
    )
    recommendation: str = Field(
        description="Recommendation for improving or maintaining good nutrition habits"  # noqa: E501
    )


async def process_meal_log(
    session: Session, user: User, meal_log: MealLogSchema
) -> MealAnalysisResult:
    try:
        row = session.scalar(
            select(BinaryData).where(BinaryData.name == BINARY_DATA_NAME)
        )
        if not row:
            raise MealAnalysisException("Meal analysis index not found in DB")

        index_binary_data = row.contents

        # Fetch all meals logged for today
        today = datetime.now().date()
        today_meals = (
            session.query(MealLog)
            .filter(MealLog.user_id == user.id, func.date(MealLog.created_at) == today)
            .all()
        )

        # Create a summary of all meals for today
        meals_summary = "\n".join(
            [
                f"{meal.meal_type.capitalize()}: {meal.description}"
                for meal in today_meals
            ]
        )

        # Get user's profile bio
        user_profile_bio = user.profile.profile_bio_data if user.profile else ""

        parser = JsonOutputParser(pydantic_object=MealAnalysisOutput)

        meal_analysis_instructions = f"""
        ### User Profile
        {user_profile_bio}

        ### Daily Meal Logging

        **All meals logged today:**
        {meals_summary}

        **New meal:**
        - {meal_log.meal_type.capitalize()}: {meal_log.description}

        Based on all the meals logged today, including the new meal, and considering the user's profile, please provide:
        1. A score (1-5) for the new meal.
        2. A summary of the meal if it's not the last meal of the day or daily meals.
        3. A brief recommendation for improving the next meal or maintaining good nutrition habits.

        Make sure to follow these guidelines:
        - Ensure the response is concise and directly addresses the points above.
        - Try to include a short compliment to the user on their meal choices if they've done well. Thought not everytime, so please gauge.
        - Make your responses variant and dynamic based on the user's meal choices and profile.
            You can highlight how the user's meal choices align with their profile and goals or affect their health.
        - Tailor your recommendations to the user's profile, considering their health goals and lifestyle.
        """  # noqa: E501

        processor = RAGProcessor(
            company_name="medo",
            user_name=user.name.split(" ")[0] if user.name else "user",
            instructions=meal_analysis_instructions,
            additional_user_context="",
            index_binary_data=index_binary_data,
            output_parser=parser,
        )

        logger.info(f"Analyzing meal logs for user {user.id}")
        analysis_result = await processor.execute(
            question=f"""
            Analyze meals for the day, including new meal:
            {meal_log.meal_type} - {meal_log.description}
            """,
        )

        # Log the raw analysis result for debugging
        logger.debug(f"Raw analysis result: {analysis_result}")

        # Directly use the dictionary to create MealAnalysisResult
        if isinstance(analysis_result, dict):
            return MealAnalysisResult(analysis=analysis_result)
        elif isinstance(analysis_result, str):
            # If it's a string, parse it
            parsed_result = parser.parse(analysis_result)
            return MealAnalysisResult(analysis=parsed_result.json())
        else:
            raise ValueError("Unexpected type for analysis_result")

    except Exception as e:
        logger.exception("Error analyzing meal log")
        raise MealAnalysisException(f"Unable to analyze meal log: {str(e)}")
