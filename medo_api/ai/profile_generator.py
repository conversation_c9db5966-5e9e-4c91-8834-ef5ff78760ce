import logging
import os

from langchain.callbacks.tracers import Lang<PERSON>hainTracer
from langchain_core.output_parsers import Str<PERSON>utputParser
from langchain_core.prompts import <PERSON>tPromptT<PERSON>plate
from langchain_openai import ChatOpenAI
from pydantic import SecretStr

from ..cache.redis_cache import RedisCache
from ..db.models import User

redis_cache = RedisCache()
logger = logging.getLogger(__name__)


async def generate_user_profile(
    user_attributes: list[dict[str, str]],
    user: User,
    enable_langsmith_tracer: bool = True,
) -> str:
    # Create a cache key based on user attributes
    attributes_hash = hash(str(sorted(str(user_attributes))))
    cache_key = f"profile_bio:{user.id}:{attributes_hash}"

    # Try to get from local+Redis cache
    cached_bio = redis_cache.get_with_local_cache(cache_key)
    if cached_bio:
        logger.info(f"Using cached profile bio for user {user.id}")
        return cached_bio

    # If not in cache, generate new bio
    llm = ChatOpenAI(
        model="gpt-4o",
        api_key=SecretStr(os.environ["OPENAI_API_KEY"]),
        timeout=30,
    )

    user_name = user.name.split(" ")[0] if user.name else "user"

    callbacks = []
    if enable_langsmith_tracer:
        callbacks.append(LangChainTracer(project_name="medo"))

    # Format user_attributes for better readability in the prompt
    formatter_user_attributes = "\n".join(
        [
            f"{key}: {value}"
            for user_attr in user_attributes
            for key, value in user_attr.items()
        ]
    )

    prompt = ChatPromptTemplate.from_template(
        """Summarize the following health survey responses into a brief user profile:

        {user_attributes}

        Create a paragraph that describes only the user's
        explicitly stated health and lifestyle factors, that impact the user's
        well-being and vitality. Adhere to these rules:
        - Use only information directly provided in the responses.
        - if no user attributes are provided, return null
        - Do not include recommendations, scores, or speculations.
        - Do not use names or personal identifiers.
        - Use all the information provided in the user_attributes and be comprehensive.
        - Do not discriminate the user based on their gender, race, or any other
        demographic information. Refer to user as 'The user' or 'user' throughout.
        - Do not comment on the amount or quality of information provided.
        - If minimal health information is given, create a profile based solely
        on what is available.
        - Focus on factual statements about the user's characteristics and habits.
        - Avoid phrases like 'Based on the context provided' or
        Just stop at generating the user's profile if no information is
        provided without having to mention explicitly that some information
        is missing or not provided

        Example 1:
        Input:
            "What is your gender?": "Male",
            "How old are you?": "36",
            "What is your weight in pounds?": "198",
            "What is your height in inches?": "79",
            "How many hours do you sleep per night?": "7-8 hours",
            "Do you smoke cigarettes, vape or use tobacco products?": "No",
            "How many days per week do you engage in moderate to vigorous physical
                activity?": "6-7 days",

        Output:
            The user is a 36-year-old male who weighs 198 pounds, is 79 inches tall,
            does not smoke or use tobacco products, and engages in moderate to vigorous
            physical activity 6-7 days a week. The user sleeps for 7-8 hours per night.
        """  # noqa: E501
    )

    chain = (prompt | llm | StrOutputParser()).with_config(
        {"run_name": "medo_profile_generator"}
    )

    result = await chain.ainvoke(
        {"user_attributes": formatter_user_attributes},
        config={
            "callbacks": callbacks,
            "tags": ["medo_profile_generator"],
            "metadata": {"company_name": "medo", "user": user_name},
        },
    )

    # Cache with profile-specific TTL
    redis_cache.set_with_ttl(cache_key, result, ttl_strategy="profile_bio")

    return result
