import asyncio
import gc
import logging
import os
from typing import Any, Dict

from pydantic import BaseModel
from sqlalchemy import select
from sqlalchemy.orm import Session

from ..cache.redis_cache import RedisCache
from ..db.models import BinaryData, InitialPillars, Pillar, PillarMatchDB, User
from ..rag_lib import RAGProcessor
from ..routers.router_utils import get_recent_meal_logs
from ..routers.sense_score_router import get_sense_score_api
from ..utils.memory_monitor import MemoryMonitor
from ..utils.settings import Settings

logger = logging.getLogger(__name__)
BINARY_DATA_NAME = os.environ["BINARY_DATA_NAME_TO_USE"]

# Initialize Redis cache
redis_cache = RedisCache()


class RecommendationGenerationException(Exception):
    pass


class PillarRecommendation(BaseModel):
    strengths: str
    areasForImprovement: str


def get_additional_user_pillar_context(
    session: Session, user: User, pillar_db_name: str
) -> str:
    print("IN GET ADDITIONAL USER PILLAR CONTEXT: ", pillar_db_name)
    initial_pillars = session.scalar(
        select(InitialPillars).where(InitialPillars.user_id == user.id)
    )

    # Create context string
    user_context = f"""
    User's {pillar_db_name} data:
    """

    # Add initial pillar data if available
    if initial_pillars and initial_pillars.additional_data:
        additional_data = initial_pillars.additional_data.get(pillar_db_name, {})
        for question, answer in additional_data.items():
            user_context += f"{question}: {answer}\n"

    # Add meal logs for nutrition pillar
    if pillar_db_name.lower() == "nutrition":
        meal_logs = get_recent_meal_logs(session, user.id, days=7)
        if meal_logs != "No meal logs available for the specified period.":
            user_context += "\nRecent meal history:\n" + meal_logs

    if not user_context.strip():
        logger.warning(f"No additional data user context found for {pillar_db_name}")

    return user_context


async def get_binary_data(session: Session) -> bytes:
    # Try to get from Redis first
    cache_key = f"binary_data:{BINARY_DATA_NAME}"
    cached_data = redis_cache.get(cache_key)

    if cached_data:
        logger.info("Using cached binary data from Redis")
        return cached_data

    # If not in cache, get from DB
    logger.info("Loading binary data from DB")
    row = session.scalar(select(BinaryData).where(BinaryData.name == BINARY_DATA_NAME))
    if not row:
        raise RecommendationGenerationException("Recommendation index not found in DB")

    # Cache the binary data
    redis_cache.set(cache_key, row.contents)
    return row.contents


async def invalidate_user_caches(user_id: int, invalidate_nutrition: bool = False):
    """Invalidate all caches related to a user when their data changes."""
    logger.info(f"Invalidating caches for user {user_id}")

    # Create patterns for all user-related caches
    cache_patterns = [
        f"user_context:{user_id}:*",
        f"sense_score:{user_id}",
        f"profile_version:{user_id}",
        f"profile_bio:{user_id}:*",
    ]

    # Add nutrition-specific patterns if meal log changed
    if invalidate_nutrition:
        cache_patterns.extend(
            [
                f"recommendation:{user_id}:nutrition",  # Only nutrition pillar
                f"user_context:{user_id}:nutrition",  # Only nutrition context
            ]
        )
    else:
        # For non-meal updates, invalidate all recommendations
        cache_patterns.append(f"recommendation:{user_id}:*")

    for pattern in cache_patterns:
        keys = redis_cache.scan_keys(pattern)
        for key in keys:
            redis_cache.delete(key)
            logger.info(f"Invalidated cache key: {key}")


async def get_cached_user_context(
    session: Session, user: User, pillar_db_name: str
) -> str:
    cache_key = f"user_context:{user.id}:{pillar_db_name}"
    cached_context = redis_cache.get(cache_key)

    if cached_context:
        logger.info(f"Using cached user context for user {user.id}")
        return cached_context

    # Build context if not cached
    context = get_additional_user_pillar_context(session, user, pillar_db_name)
    users_profile_bio = user.profile.profile_bio_data if user.profile else ""
    context += users_profile_bio

    # Cache for 1 hour
    redis_cache.set(cache_key, context, expire=3600)
    return context


async def get_cached_sense_score(session: Session, user: User) -> Dict[str, Any]:
    cache_key = f"sense_score:{user.id}"
    cached_score = redis_cache.get(cache_key)

    if cached_score:
        logger.info(f"Using cached SENSE score for user {user.id}")
        return cached_score

    # Get fresh score if not cached
    sense_score_response = await get_sense_score_api(user=user, session=session)
    score_data = {
        "score": sense_score_response.senseScore,
        "pillars": sense_score_response.pillars.dict(),
    }

    # Cache for 30 minutes
    redis_cache.set(cache_key, score_data, expire=1800)
    return score_data


def get_pillar_specific_context(pillar: Pillar, user_context: str) -> str:
    if pillar.value.lower() == "nutrition":
        return """
        When analyzing nutrition:
        - Consider the recent meal logs in the user context
        - Look for patterns in meal timing and types
        - Consider meal variety and nutritional balance
        - Factor in meal consistency and portion sizes
        - Use meal history to identify both strengths and areas for improvement
        """
    return ""


async def generate_pillar_recommendation(
    session: Session,
    user: User,
    pillar: Pillar,
) -> PillarRecommendation:
    try:
        MemoryMonitor.log_memory_usage()

        # Check profile version before using cache
        cache_key = f"recommendation:{user.id}:{pillar.value}"
        profile_version_key = f"profile_version:{user.id}"

        cached_version = redis_cache.get(profile_version_key)
        current_version = user.profile.updated_at.timestamp() if user.profile else 0

        cached_recommendation = None
        if cached_version and float(cached_version) >= current_version:
            cached_recommendation = redis_cache.get(cache_key)

        if cached_recommendation:
            logger.info(f"Using cached recommendation for {pillar.value}")
            return PillarRecommendation(**cached_recommendation)

        # Get cached data
        index_binary_data = await get_binary_data(session)
        user_context = await get_cached_user_context(
            session, user, PillarMatchDB[pillar.value].value
        )
        sense_score_data = await get_cached_sense_score(session, user)

        # Format sense score context
        sense_score_context = f"""
        User's SENSE Score: {sense_score_data['score']}
        User's Pillar Scores:
        - Sleep: {sense_score_data['pillars']['sleep']}
        - Exercise: {sense_score_data['pillars']['exercise']}
        - Nutrition: {sense_score_data['pillars']['nutrition']}
        - Social Connection: {sense_score_data['pillars']['socialConnection']}
        - Emotional Wellbeing: {sense_score_data['pillars']['emotionalWellbeing']}
        """

        pillar_context = get_pillar_specific_context(pillar, user_context)
        
        strength_instructions = f"""
        {sense_score_context}
        {pillar_context}

        Identify 2-3 personalised key strengths in {pillar.value} based on users's
        specific profile and health data without any introductory text:
        - [Brief, actionable, motivating strength description]
        - [Brief, actionable, motivating strength description]
        - [Brief, actionable, motivating strength description] (if applicable)

        Each strength must be:
        - One sentence long
        - Show how it can be applied or leveraged
        - Use positive, encouraging language
        - Focus on the user's unique profile and health data and other user's relevant context

        Examples (do not include these in your response):
        1. For high "Sleep" score (consistent schedule):
        - Your regular sleep schedule enhances overall sleep quality and daytime energy,
        supporting natural circadian rhythms and long-term health.

        2. For high "Nutrition" score (vegetable intake):
        - Your high vegetable consumption provides essential nutrients, supporting
        overall health, longevity, better digestion, and a stronger immune system.

        3. For high "Exercise" score (cardio activities):
        - Your commitment to regular cardio exercise strengthens your heart, improves
        cardiovascular health, boosts energy levels, and supports weight management.

        Provide your response using only bullet points, without any additional text
        before or after the list.

        {Settings.USERS_PROFILE_INSTRUCTIONS}
        """

        improvement_instructions = f"""
        {sense_score_context}
        {pillar_context}

        Provide 2-3 actionable and personalised improvement suggestions

        for {pillar.value} based on users's specific profile and health data
        without any introductory text:
        - [Brief, motivating, practical suggestion]
        - [Brief, motivating, practical suggestion]
        - [Brief, motivating, practical suggestion] (if applicable)

        Each suggestion must be:
        - One sentence long
        - Use encouraging language
        - Focus on easily implementable steps
        - Focus on the user's unique profile and health data and other user's relevant context

        Examples (do not include these in your response):
        1. For low "Sleep" score (sleep environment):
        - Create a dark, quiet sleep space using blackout curtains and a white noise
        machine to improve sleep quality.

        2. For low "Nutrition" score (meal planning):
        - Prep healthy snacks like cut vegetables and fruits for easy access during the
        week, supporting better nutritional choices.

        3. For low "Exercise" score (consistency):
        - Schedule short, 10-minute workouts at the same time each day to build a
        consistent exercise habit and improve overall fitness.

        Provide your response using only bullet points, without any additional text
        before or after the list.

        {Settings.USERS_PROFILE_INSTRUCTIONS}
        """

        user_name = user.name.split(" ")[0] if user.name else "user"

        # Create two RAGProcessor instances with cleanup and Redis cache
        try:
            processor_strengths = RAGProcessor(
                company_name="medo",
                user_name=user_name,
                instructions=strength_instructions,
                additional_user_context=user_context,
                index_binary_data=index_binary_data,
                redis_cache=redis_cache,
            )

            processor_improvements = RAGProcessor(
                company_name="medo",
                user_name=user_name,
                instructions=improvement_instructions,
                additional_user_context=user_context,
                index_binary_data=index_binary_data,
                redis_cache=redis_cache,
            )

            # Define async functions for each execution
            async def get_strengths():
                try:
                    logger.info(f"Creating strength recommendation for {pillar.value}")
                    return await processor_strengths.execute(
                        question=f"Recommend strengths for {pillar.value}"
                    )
                finally:
                    processor_strengths.cleanup()

            async def get_improvements():
                try:
                    logger.info(
                        f"Creating areas of improvement recommendation for {pillar.value}"
                    )
                    return await processor_improvements.execute(
                        question=f"Recommend improvements for {pillar.value}"
                    )
                finally:
                    processor_improvements.cleanup()

            # Run the LLM calls asynchronously using asyncio.gather
            strengths_result, areas_for_improvement_result = await asyncio.gather(
                get_strengths(), get_improvements()
            )

            recommendation = PillarRecommendation(
                strengths=strengths_result.strip(),
                areasForImprovement=areas_for_improvement_result.strip(),
            )

            # When caching the new recommendation, also update profile version
            redis_cache.set(cache_key, recommendation.dict(), expire=3600)
            redis_cache.set(profile_version_key, current_version, expire=3600)

            return recommendation

        finally:
            gc.collect()
            MemoryMonitor.log_memory_usage()

    except Exception as e:
        logger.exception("Error generating pillar recommendation")
        raise RecommendationGenerationException(
            f"Unable to generate recommendation for {pillar.value}: {str(e)}"
        )
