import logging
import os
import pickle
import time
from dataclasses import dataclass
from functools import lru_cache
from typing import Any, Dict, Optional

import redis
from redis.connection import ConnectionPool

logger = logging.getLogger(__name__)


@dataclass
class CacheStats:
    hits: int = 0
    misses: int = 0
    total_hit_time: float = 0
    total_miss_time: float = 0


class RedisCache:
    _instance = None
    _pool = None
    _stats: Dict[str, CacheStats] = {}

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(RedisCache, cls).__new__(cls)
            redis_url = os.getenv("REDISCLOUD_URL", "redis://localhost:6379/0")
            cls._pool = ConnectionPool.from_url(redis_url)
        return cls._instance

    @property
    def client(self) -> redis.Redis:
        return redis.Redis(connection_pool=self._pool)

    def set(self, key: str, value: Any, expire: int = 3600) -> bool:
        try:
            serialized = pickle.dumps(value)
            return self.client.set(key, serialized, ex=expire)
        except Exception as e:
            logger.error(f"Error setting cache key {key}: {str(e)}")
            return False

    def get(self, key: str) -> Optional[Any]:
        start_time = time.time()
        try:
            if key not in self._stats:
                self._stats[key] = CacheStats()

            data = self.client.get(key)
            elapsed = time.time() - start_time

            if data:
                self._stats[key].hits += 1
                self._stats[key].total_hit_time += elapsed
                logger.info(f"Cache HIT for key {key} in {elapsed*1000:.2f}ms")
                return pickle.loads(data)
            else:
                self._stats[key].misses += 1
                self._stats[key].total_miss_time += elapsed
                logger.info(f"Cache MISS for key {key} in {elapsed*1000:.2f}ms")

        except Exception as e:
            logger.error(f"Error getting cache key {key}: {str(e)}")
        return None

    def get_stats(self) -> Dict[str, Dict]:
        stats = {}
        for key, stat in self._stats.items():
            total_requests = stat.hits + stat.misses
            hit_rate = stat.hits / total_requests if total_requests > 0 else 0
            avg_hit_time = stat.total_hit_time / stat.hits if stat.hits > 0 else 0
            avg_miss_time = stat.total_miss_time / stat.misses if stat.misses > 0 else 0

            stats[key] = {
                "hits": stat.hits,
                "misses": stat.misses,
                "hit_rate": round(hit_rate, 3),
                "avg_hit_time_ms": round(avg_hit_time * 1000, 2),
                "avg_miss_time_ms": round(avg_miss_time * 1000, 2),
                "total_requests": total_requests,
            }
        return stats

    def scan_keys(self, pattern: str) -> list:
        """Scan and return all keys matching the pattern."""
        keys = []
        cursor = 0
        while True:
            cursor, partial_keys = self.client.scan(cursor, match=pattern)
            keys.extend(partial_keys)
            if cursor == 0:
                break
        return keys

    def delete(self, key: str) -> bool:
        """Delete a key from Redis."""
        try:
            return bool(self.client.delete(key))
        except Exception as e:
            logger.error(f"Error deleting cache key {key}: {str(e)}")
            return False

    def set_with_version(
        self, key: str, value: Any, version: float, expire: int = 3600
    ) -> bool:
        """Set cache with version tracking."""
        try:
            serialized = pickle.dumps({"value": value, "version": version})
            return self.client.set(key, serialized, ex=expire)
        except Exception as e:
            logger.error(f"Error setting cache key {key}: {str(e)}")
            return False

    def get_with_version(self, key: str, current_version: float) -> Optional[Any]:
        """Get cache if version is current."""
        try:
            data = self.client.get(key)
            if data:
                cached_data = pickle.loads(data)
                if cached_data["version"] >= current_version:
                    return cached_data["value"]
            return None
        except Exception as e:
            logger.error(f"Error getting cache key {key}: {str(e)}")
            return None

    @lru_cache(maxsize=1000)  # Local LRU cache
    def get_with_local_cache(self, key: str) -> Optional[Any]:
        """Get value with local LRU cache backup"""
        try:
            return self.get(key)
        except Exception as e:
            logger.error(f"Redis error: {e}")
            return None

    def set_with_ttl(self, key: str, value: Any, ttl_strategy: str = "default") -> bool:
        """Set value with dynamic TTL based on data type"""
        try:
            ttl_mapping = {
                "profile_bio": 86400,  # 24 hours for profile bios
                "user_context": 3600,  # 1 hour for user context
                "recommendation": 3600,  # 1 hour for recommendations
                "sense_score": 1800,  # 30 minutes for sense scores
                "default": 3600,  # 1 hour default
            }

            ttl = ttl_mapping.get(ttl_strategy, ttl_mapping["default"])
            return self.set(key, value, expire=ttl)
        except Exception as e:
            logger.error(f"Error setting cache with TTL: {e}")
            return False
