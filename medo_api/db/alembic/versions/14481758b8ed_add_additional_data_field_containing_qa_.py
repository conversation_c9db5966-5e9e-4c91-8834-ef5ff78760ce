"""add additional data field containing QA pairs to Pillars

Revision ID: 14481758b8ed
Revises: b3c010100cc1
Create Date: 2024-09-25 05:28:59.378932

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = "14481758b8ed"
down_revision: Union[str, None] = "b3c010100cc1"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Add the column as nullable first
    op.add_column(
        "initial_pillars",
        sa.Column("additional_data", postgresql.JSONB(), nullable=True),
    )

    # Update existing rows with an empty JSON object
    op.execute(
        "UPDATE initial_pillars SET additional_data = '{}'::jsonb WHERE additional_data IS NULL"
    )

    # Alter the column to be non-nullable
    op.alter_column("initial_pillars", "additional_data", nullable=False)


def downgrade() -> None:
    op.drop_column("initial_pillars", "additional_data")
