"""Add Recommendation model

Revision ID: 2a4c9d8e55a4
Revises: e349b3676adb
Create Date: 2024-08-18 13:21:17.962781

"""

from typing import Sequence, Union


# revision identifiers, used by Alembic.
revision: str = "2a4c9d8e55a4"
down_revision: Union[str, None] = "e349b3676adb"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###
