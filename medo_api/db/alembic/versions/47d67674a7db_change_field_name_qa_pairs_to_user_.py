"""Change field name qa_pairs to user_attributes

Revision ID: 47d67674a7db
Revises: bde005bbbd36
Create Date: 2024-10-04 08:34:59.401706

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = "47d67674a7db"
down_revision: Union[str, None] = "bde005bbbd36"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade():
    # Add the new column as nullable first
    op.add_column(
        "user_profile", sa.Column("user_attributes", postgresql.JSON(), nullable=True)
    )

    # Copy data from qa_pairs to user_attributes
    op.execute("UPDATE user_profile SET user_attributes = qa_pairs::json")

    # Make user_attributes not nullable
    op.alter_column("user_profile", "user_attributes", nullable=False)

    # Drop the old qa_pairs column
    op.drop_column("user_profile", "qa_pairs")


def downgrade():
    # Add back the qa_pairs column
    op.add_column(
        "user_profile", sa.Column("qa_pairs", postgresql.JSON(), nullable=True)
    )

    # Copy data from user_attributes to qa_pairs
    op.execute("UPDATE user_profile SET qa_pairs = user_attributes::json")

    # Make qa_pairs not nullable
    op.alter_column("user_profile", "qa_pairs", nullable=False)

    # Drop the user_attributes column
    op.drop_column("user_profile", "user_attributes")
