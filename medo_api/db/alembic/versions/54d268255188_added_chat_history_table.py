"""Added chat_history table

Revision ID: 54d268255188
Revises: efaa9b249b83
Create Date: 2024-04-04 14:29:13.068800

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "54d268255188"
down_revision: Union[str, None] = "efaa9b249b83"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "chat_history",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("question", sa.String(), nullable=False),
        sa.Column("answer", sa.String(), nullable=False),
        sa.<PERSON>umn("user_id", sa.Integer(), nullable=False),
        sa.<PERSON>umn("created_at", sa.DateTime(), nullable=False),
        sa.ForeignKeyConstraint(
            ["user_id"],
            ["users.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_chat_history_user_id"), "chat_history", ["user_id"], unique=False
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_chat_history_user_id"), table_name="chat_history")
    op.drop_table("chat_history")
    # ### end Alembic commands ###
