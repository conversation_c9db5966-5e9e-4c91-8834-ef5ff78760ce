"""new survey capture API

Revision ID: 577110ab47ac
Revises: 8f8a3311c007
Create Date: 2024-08-06 14:53:23.300823

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "577110ab47ac"
down_revision: Union[str, None] = "8f8a3311c007"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "initial_pillars", sa.Column("typeform_result", sa.JSON(), nullable=False)
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("initial_pillars", "typeform_result")
    # ### end Alembic commands ###
