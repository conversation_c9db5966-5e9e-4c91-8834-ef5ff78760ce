"""Add checkins

Revision ID: 5c275a0320ed
Revises: c5668cb43c2d
Create Date: 2024-06-13 16:24:13.338207

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "5c275a0320ed"
down_revision: Union[str, None] = "c5668cb43c2d"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "checkin",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.<PERSON>umn("user_id", sa.Integer(), nullable=False),
        sa.<PERSON>umn("nutrition", sa.String(), nullable=False),
        sa.<PERSON>umn("social_connection", sa.Integer(), nullable=False),
        sa.<PERSON>umn("emotional_wellbeing", sa.Integer(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=False),
        sa.Column("updated_at", sa.DateTime(), nullable=False),
        sa.ForeignKeyConstraint(
            ["user_id"],
            ["user.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_checkin_user_id"), "checkin", ["user_id"], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_checkin_user_id"), table_name="checkin")
    op.drop_table("checkin")
    # ### end Alembic commands ###
