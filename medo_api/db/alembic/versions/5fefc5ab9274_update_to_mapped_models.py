"""Update to mapped models

Revision ID: 5fefc5ab9274
Revises: cb8784c66be7
Create Date: 2024-05-27 13:52:03.903087

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = "5fefc5ab9274"
down_revision: Union[str, None] = "cb8784c66be7"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "user",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("firebase_uid", sa.String(), nullable=False),
        sa.Column("name", sa.String(), nullable=True),
        sa.Column("email", sa.String(), nullable=True),
        sa.Column("created_at", sa.DateTime(), nullable=False),
        sa.Column("updated_at", sa.DateTime(), nullable=False),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_user_firebase_uid"), "user", ["firebase_uid"], unique=True)
    op.drop_table("api_tokens")
    op.drop_index("ix_users_email", table_name="users")
    op.drop_constraint("chat_history_user_id_fkey", "chat_history", type_="foreignkey")
    op.drop_table("users")
    op.drop_constraint("binary_data_name_key", "binary_data", type_="unique")
    op.create_index(op.f("ix_binary_data_name"), "binary_data", ["name"], unique=True)
    op.create_foreign_key(None, "chat_history", "user", ["user_id"], ["id"])
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, "chat_history", type_="foreignkey")  # type: ignore
    op.create_foreign_key(
        "chat_history_user_id_fkey", "chat_history", "users", ["user_id"], ["id"]
    )
    op.drop_index(op.f("ix_binary_data_name"), table_name="binary_data")
    op.create_unique_constraint("binary_data_name_key", "binary_data", ["name"])
    op.create_table(
        "users",
        sa.Column("id", sa.INTEGER(), autoincrement=True, nullable=False),
        sa.Column("name", sa.VARCHAR(), autoincrement=False, nullable=False),
        sa.Column("email", sa.VARCHAR(), autoincrement=False, nullable=False),
        sa.Column(
            "created_at", postgresql.TIMESTAMP(), autoincrement=False, nullable=False
        ),
        sa.Column(
            "updated_at", postgresql.TIMESTAMP(), autoincrement=False, nullable=False
        ),
        sa.Column("firebase_uid", sa.VARCHAR(), autoincrement=False, nullable=False),
        sa.PrimaryKeyConstraint("id", name="users_pkey"),
    )
    op.create_index("ix_users_email", "users", ["email"], unique=True)
    op.create_table(
        "api_tokens",
        sa.Column("id", sa.INTEGER(), autoincrement=True, nullable=False),
        sa.Column("token", sa.VARCHAR(), autoincrement=False, nullable=False),
        sa.Column(
            "created_at", postgresql.TIMESTAMP(), autoincrement=False, nullable=False
        ),
        sa.PrimaryKeyConstraint("id", name="api_tokens_pkey"),
        sa.UniqueConstraint("token", name="api_tokens_token_key"),
    )
    op.drop_index(op.f("ix_user_firebase_uid"), table_name="user")
    op.drop_table("user")
    # ### end Alembic commands ###
