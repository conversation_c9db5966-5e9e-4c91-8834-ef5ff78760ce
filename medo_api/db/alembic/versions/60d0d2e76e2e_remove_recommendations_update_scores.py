"""Remove recommendations, update scores

Revision ID: 60d0d2e76e2e
Revises: ab30bb9a0bcb
Create Date: 2024-09-05 15:52:50.201586

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = "60d0d2e76e2e"
down_revision: Union[str, None] = "ab30bb9a0bcb"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index("ix_recommendation_user_id", table_name="recommendation")
    op.drop_table("recommendation")
    op.add_column(
        "initial_pillars", sa.Column("sense_score", sa.Integer(), nullable=False)
    )
    op.drop_column("user", "sense_score")
    op.drop_column("user", "previous_sense_score_date")
    op.drop_column("user", "sense_score_date")
    op.drop_column("user", "previous_sense_score")
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "user",
        sa.Column(
            "previous_sense_score", sa.INTEGER(), autoincrement=False, nullable=True
        ),
    )
    op.add_column(
        "user",
        sa.Column(
            "sense_score_date",
            postgresql.TIMESTAMP(),
            autoincrement=False,
            nullable=True,
        ),
    )
    op.add_column(
        "user",
        sa.Column(
            "previous_sense_score_date",
            postgresql.TIMESTAMP(),
            autoincrement=False,
            nullable=True,
        ),
    )
    op.add_column(
        "user",
        sa.Column("sense_score", sa.INTEGER(), autoincrement=False, nullable=True),
    )
    op.drop_column("initial_pillars", "sense_score")
    op.create_table(
        "recommendation",
        sa.Column("id", sa.INTEGER(), autoincrement=True, nullable=False),
        sa.Column("user_id", sa.INTEGER(), autoincrement=False, nullable=False),
        sa.Column("category", sa.VARCHAR(), autoincrement=False, nullable=False),
        sa.Column("content", sa.VARCHAR(), autoincrement=False, nullable=False),
        sa.Column("read", sa.BOOLEAN(), autoincrement=False, nullable=False),
        sa.Column(
            "created_at", postgresql.TIMESTAMP(), autoincrement=False, nullable=False
        ),
        sa.Column(
            "updated_at", postgresql.TIMESTAMP(), autoincrement=False, nullable=False
        ),
        sa.ForeignKeyConstraint(
            ["user_id"], ["user.id"], name="recommendation_user_id_fkey"
        ),
        sa.PrimaryKeyConstraint("id", name="recommendation_pkey"),
    )
    op.create_index(
        "ix_recommendation_user_id", "recommendation", ["user_id"], unique=False
    )
    # ### end Alembic commands ###
