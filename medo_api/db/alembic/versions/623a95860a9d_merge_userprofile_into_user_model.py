"""Merge UserProfile into User model

Revision ID: 623a95860a9d
Revises: 2a4c9d8e55a4
Create Date: 2024-08-19 18:15:31.351019

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = "623a95860a9d"
down_revision: Union[str, None] = "2a4c9d8e55a4"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index("ix_user_profile_user_id", table_name="user_profile")
    op.drop_table("user_profile")
    op.add_column("user", sa.Column("age", sa.Integer(), nullable=True))
    op.add_column("user", sa.Column("gender", sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("user", "gender")
    op.drop_column("user", "age")
    op.create_table(
        "user_profile",
        sa.Column("id", sa.INTEGER(), autoincrement=True, nullable=False),
        sa.Column("user_id", sa.INTEGER(), autoincrement=False, nullable=False),
        sa.Column("name", sa.VARCHAR(), autoincrement=False, nullable=False),
        sa.Column("age", sa.INTEGER(), autoincrement=False, nullable=False),
        sa.Column("gender", sa.VARCHAR(), autoincrement=False, nullable=False),
        sa.Column(
            "created_at", postgresql.TIMESTAMP(), autoincrement=False, nullable=False
        ),
        sa.Column(
            "updated_at", postgresql.TIMESTAMP(), autoincrement=False, nullable=False
        ),
        sa.ForeignKeyConstraint(
            ["user_id"], ["user.id"], name="user_profile_user_id_fkey"
        ),
        sa.PrimaryKeyConstraint("id", name="user_profile_pkey"),
    )
    op.create_index(
        "ix_user_profile_user_id", "user_profile", ["user_id"], unique=False
    )
    # ### end Alembic commands ###
