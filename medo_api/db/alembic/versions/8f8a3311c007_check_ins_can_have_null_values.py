"""Check-ins can have NULL values

Revision ID: 8f8a3311c007
Revises: e0ce8602ecec
Create Date: 2024-06-27 20:51:04.284162

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "8f8a3311c007"
down_revision: Union[str, None] = "e0ce8602ecec"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column("checkin", "nutrition", existing_type=sa.VARCHAR(), nullable=True)
    op.alter_column(
        "checkin", "social_connection", existing_type=sa.INTEGER(), nullable=True
    )
    op.alter_column(
        "checkin", "emotional_wellbeing", existing_type=sa.INTEGER(), nullable=True
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "checkin", "emotional_wellbeing", existing_type=sa.INTEGER(), nullable=False
    )
    op.alter_column(
        "checkin", "social_connection", existing_type=sa.INTEGER(), nullable=False
    )
    op.alter_column("checkin", "nutrition", existing_type=sa.VARCHAR(), nullable=False)
    # ### end Alembic commands ###
