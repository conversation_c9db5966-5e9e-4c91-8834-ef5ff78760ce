"""change age to dob in user model

Revision ID: 9fcf1ea6bbda
Revises: 623a95860a9d
Create Date: 2024-08-23 13:04:46.098554

"""

from typing import Sequence, Union


# revision identifiers, used by Alembic.
revision: str = "9fcf1ea6bbda"
down_revision: Union[str, None] = "623a95860a9d"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###
