"""Change to DOB

Revision ID: ab30bb9a0bcb
Revises: 9fcf1ea6bbda
Create Date: 2024-08-26 13:48:46.792797

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "ab30bb9a0bcb"
down_revision: Union[str, None] = "9fcf1ea6bbda"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "recommendation",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("user_id", sa.Integer(), nullable=False),
        sa.Column("category", sa.String(), nullable=False),
        sa.Column("content", sa.String(), nullable=False),
        sa.Column("read", sa.<PERSON>(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=False),
        sa.Column("updated_at", sa.DateTime(), nullable=False),
        sa.ForeignKeyConstraint(
            ["user_id"],
            ["user.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_recommendation_user_id"), "recommendation", ["user_id"], unique=False
    )
    op.add_column("user", sa.Column("dob", sa.DateTime(), nullable=True))
    op.drop_column("user", "age")
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "user", sa.Column("age", sa.INTEGER(), autoincrement=False, nullable=True)
    )
    op.drop_column("user", "dob")
    op.drop_index(op.f("ix_recommendation_user_id"), table_name="recommendation")
    op.drop_table("recommendation")
    # ### end Alembic commands ###
