"""Add meal logs

Revision ID: b3c010100cc1
Revises: 60d0d2e76e2e
Create Date: 2024-09-20 15:19:32.837211

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "b3c010100cc1"
down_revision: Union[str, None] = "60d0d2e76e2e"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "meal_log",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.<PERSON>umn("user_id", sa.Integer(), nullable=False),
        sa.<PERSON>umn("meal_type", sa.String(), nullable=False),
        sa.Column("description", sa.String(), nullable=False),
        sa.Column("analysis", sa.String(), nullable=False),
        sa.Column("is_final_meal", sa.<PERSON>(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=False),
        sa.Column("updated_at", sa.DateTime(), nullable=False),
        sa.ForeignKeyConstraint(
            ["user_id"],
            ["user.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_meal_log_user_id"), "meal_log", ["user_id"], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_meal_log_user_id"), table_name="meal_log")
    op.drop_table("meal_log")
    # ### end Alembic commands ###
