"""add unique constraint on - each user with one profile at most

Revision ID: b8cb021cd840
Revises: e2254e734881
Create Date: 2024-10-04 06:09:51.756848

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'b8cb021cd840'
down_revision: Union[str, None] = 'e2254e734881'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('ix_user_profile_user_id', table_name='user_profile')
    op.create_index(op.f('ix_user_profile_user_id'), 'user_profile', ['user_id'], unique=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_user_profile_user_id'), table_name='user_profile')
    op.create_index('ix_user_profile_user_id', 'user_profile', ['user_id'], unique=False)
    # ### end Alembic commands ###
