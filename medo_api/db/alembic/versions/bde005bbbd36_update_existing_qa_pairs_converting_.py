"""update existing qa_pairs converting dict to list

Revision ID: bde005bbbd36
Revises: b8cb021cd840
Create Date: 2024-10-04 06:18:09.090681

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects.postgresql import JSON


# revision identifiers, used by Alembic.
revision: str = "bde005bbbd36"
down_revision: Union[str, None] = "b8cb021cd840"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade():
    # Create a temporary column to store the new list format
    op.add_column("user_profile", sa.Column("qa_pairs_list", JSON))

    # Update the data
    op.execute(
        """
    UPDATE user_profile
    SET qa_pairs_list = (
        SELECT json_agg(json_build_object('question', key, 'answer', value))
        FROM json_each(qa_pairs::json)
    )
    WHERE qa_pairs IS NOT NULL
    """
    )

    # Drop the old column and rename the new one
    op.drop_column("user_profile", "qa_pairs")
    op.alter_column("user_profile", "qa_pairs_list", new_column_name="qa_pairs")


def downgrade():
    # Create a temporary column to store the old dict format
    op.add_column("user_profile", sa.Column("qa_pairs_dict", JSON))

    # Update the data back to the dict format
    op.execute(
        """
    UPDATE user_profile
    SET qa_pairs_dict = (
        SELECT json_object_agg(qa->>'question', qa->>'answer')
        FROM json_array_elements(qa_pairs::json) AS qa
    )
    WHERE qa_pairs IS NOT NULL
    """
    )

    # Drop the list column and rename the dict column
    op.drop_column("user_profile", "qa_pairs")
    op.alter_column("user_profile", "qa_pairs_dict", new_column_name="qa_pairs")
