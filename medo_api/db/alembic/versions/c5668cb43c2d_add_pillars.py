"""Add Pillars

Revision ID: c5668cb43c2d
Revises: 5fefc5ab9274
Create Date: 2024-05-27 19:40:42.242984

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "c5668cb43c2d"
down_revision: Union[str, None] = "5fefc5ab9274"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "initial_pillars",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("user_id", sa.Integer(), nullable=False),
        sa.<PERSON>umn("sleep", sa.Integer(), nullable=False),
        sa.Column("exercise", sa.Integer(), nullable=False),
        sa.Column("nutrition", sa.Integer(), nullable=False),
        sa.Column("social_connection", sa.Integer(), nullable=False),
        sa.Column("emotional_wellbeing", sa.Integer(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=False),
        sa.Column("updated_at", sa.DateTime(), nullable=False),
        sa.ForeignKeyConstraint(
            ["user_id"],
            ["user.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_initial_pillars_user_id"), "initial_pillars", ["user_id"], unique=False
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_initial_pillars_user_id"), table_name="initial_pillars")
    op.drop_table("initial_pillars")
    # ### end Alembic commands ###
