"""Replace Kinde Id with Firebase UID

Revision ID: cb8784c66be7
Revises: 013ecc4bac91
Create Date: 2024-05-07 16:35:16.946300

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "cb8784c66be7"
down_revision: Union[str, None] = "013ecc4bac91"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("users", sa.Column("firebase_uid", sa.String(), nullable=False))
    op.drop_column("users", "kinde_id")
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "users",
        sa.Column("kinde_id", sa.VARCHAR(), autoincrement=False, nullable=False),
    )
    op.drop_column("users", "firebase_uid")
    # ### end Alembic commands ###
