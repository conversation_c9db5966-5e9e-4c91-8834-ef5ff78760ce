"""Store meal_log analysis as JSON type

Revision ID: dd2c0f6e1500
Revises: 47d67674a7db
Create Date: 2024-10-23 23:34:54.470985

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "dd2c0f6e1500"
down_revision: Union[str, None] = "47d67674a7db"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Convert existing strings to JSON format
    op.execute(
        """
        UPDATE meal_log
        SET analysis = json_build_object(
            'summary', analysis,
            'score', NULL,
            'recommendations', NULL
        )::json
    """
    )

    # Alter the column with a USING clause to convert existing data
    op.alter_column(
        "meal_log", "analysis", type_=sa.JSON, postgresql_using="analysis::json"
    )


def downgrade() -> None:
    # Revert the column type back to String
    op.alter_column(
        "meal_log", "analysis", type_=sa.String, postgresql_using="analysis::text"
    )
