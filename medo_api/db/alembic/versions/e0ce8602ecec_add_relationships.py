"""Add relationships

Revision ID: e0ce8602ecec
Revises: 5c275a0320ed
Create Date: 2024-06-13 18:37:03.205745

"""

from typing import Sequence, Union


# revision identifiers, used by Alembic.
revision: str = "e0ce8602ecec"
down_revision: Union[str, None] = "5c275a0320ed"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###
