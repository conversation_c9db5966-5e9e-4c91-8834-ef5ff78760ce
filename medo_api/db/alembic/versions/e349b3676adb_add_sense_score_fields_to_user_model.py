"""Add sense score fields to User model

Revision ID: e349b3676adb
Revises: 29eb893f2a81
Create Date: 2024-08-16 20:30:05.766791

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "e349b3676adb"
down_revision: Union[str, None] = "29eb893f2a81"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("user", sa.Column("sense_score", sa.Integer(), nullable=True))
    op.add_column("user", sa.Column("sense_score_date", sa.DateTime(), nullable=True))
    op.add_column(
        "user", sa.Column("previous_sense_score", sa.Integer(), nullable=True)
    )
    op.add_column(
        "user", sa.Column("previous_sense_score_date", sa.DateTime(), nullable=True)
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("user", "previous_sense_score_date")
    op.drop_column("user", "previous_sense_score")
    op.drop_column("user", "sense_score_date")
    op.drop_column("user", "sense_score")
    # ### end Alembic commands ###
