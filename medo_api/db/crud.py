import logging
from datetime import date, datetime, timedelta
from functools import lru_cache
from typing import Any, Sequence

from sqlalchemy import asc, func, select
from sqlalchemy.orm import Session, joinedload

from .models import BinaryData, ChatHistory, Checkin, MealLog, User, UserProfile

logger = logging.getLogger(__name__)


class RecommendationGenerationException(Exception):
    pass


# region BINARY_DATA/INDEX
def get_index_by_id(session: Session, id: int) -> BinaryData | None:
    return session.scalar(select(BinaryData).where(BinaryData.id == id))


def get_index_by_name(session: Session, name: str) -> BinaryData | None:
    return session.scalar(select(BinaryData).where(BinaryData.name == name))


def create_index(session: Session, index_to_add: BinaryData) -> BinaryData | None:
    index_to_be_created = BinaryData(
        name=index_to_add.name, contents=index_to_add.contents
    )
    session.add(index_to_be_created)
    session.commit()
    return index_to_be_created


# endregion BINARY_DATA/INDEX


# region USER
def get_user(session: Session, id: int) -> User | None:
    return session.scalar(select(User).where(User.id == id))


def get_all_users(session: Session) -> list[User]:
    return session.scalars(select(User)).all()  # type: ignore


def get_user_by_firebase_uid(session: Session, firebase_uid: str) -> User | None:
    return session.scalar(
        select(User)
        .options(joinedload(User.profile))
        .where(User.firebase_uid == firebase_uid)
    )


def create_user(
    session: Session,
    firebase_uid: str,
    name: str | None,
    email: str | None,
    dob: date | None,
    gender: str | None,
) -> User:
    user = User(
        firebase_uid=firebase_uid,
        name=name,
        email=email,
        dob=datetime.combine(dob, datetime.min.time()) if dob else None,
        gender=gender,
    )
    session.add(user)
    session.commit()
    return user


def update_user(session: Session, user: User, update_data: dict[str, Any]) -> User:
    profile_data = {}
    for key, value in update_data.items():
        if hasattr(user, key):
            if key == "dob":
                if isinstance(value, str):
                    # Convert string to datetime object
                    value = datetime.strptime(value, "%Y-%m-%d")
                elif isinstance(value, date):
                    # Convert date to datetime
                    value = datetime.combine(value, datetime.min.time())
            setattr(user, key, value)
        elif key in ["profile_bio_data", "user_attributes"]:
            profile_data[key] = value

    if profile_data:
        if user.profile is None:
            user.profile = UserProfile(
                user=user,
                user_attributes=profile_data.get("user_attributes", []),
                profile_bio_data=profile_data.get("profile_bio_data", ""),
            )
        else:
            for key, value in profile_data.items():
                setattr(user.profile, key, value)

    session.add(user)
    session.commit()
    session.refresh(user)
    return user


# endregion USER


# region CHAT_HISTORY
def create_chat_history(
    session: Session, user: User, question: str, answer: str
) -> ChatHistory:
    chat_history = ChatHistory(user=user, question=question, answer=answer)
    session.add(chat_history)
    session.commit()
    return chat_history


def delete_chat_history_range(
    session: Session, user: User, days_ago_start: int, days_ago_end: int
) -> int:
    """Delete chat history within a specific date range.

    Args:
        session: Database session
        user: User whose chat history to delete
        days_ago_start: Start of range (more recent)
        days_ago_end: End of range (older)

    Returns:
        Number of records deleted
    """
    end_date = datetime.utcnow() - timedelta(days=days_ago_start)
    start_date = datetime.utcnow() - timedelta(days=days_ago_end)

    result = (
        session.query(ChatHistory)
        .where(
            ChatHistory.user_id == user.id,
            ChatHistory.created_at >= start_date,
            ChatHistory.created_at <= end_date,
        )
        .delete(synchronize_session=False)
    )

    session.commit()
    return result


# endregion CHAT_HISTORY


# region CHECKIN
def create_checkin(
    session: Session,
    user: User,
    nutrition: str | None = None,
    social_connection: int | None = None,
    emotional_wellbeing: int | None = None,
) -> Checkin:
    checkin = Checkin(
        user=user,
        nutrition=nutrition,
        social_connection=social_connection,
        emotional_wellbeing=emotional_wellbeing,
    )
    session.add(checkin)
    session.commit()
    return checkin


def get_checkins(
    session: Session, user: User, from_datetime: datetime | None
) -> Sequence[Checkin]:
    stmt = select(Checkin).where(Checkin.user_id == user.id)
    if from_datetime is not None:
        stmt = stmt.where(Checkin.created_at >= from_datetime)
    stmt = stmt.order_by(asc(Checkin.created_at))
    return session.scalars(stmt).all()


# endregion CHECKIN


# region MEAL_LOG
def create_meal_log(
    session: Session,
    user: User,
    meal_type: str,
    description: str,
    analysis: dict[str, Any],  # Change the type to accept a dictionary
    is_final_meal: bool = False,
) -> MealLog:
    meal_log = MealLog(
        user=user,
        meal_type=meal_type,
        description=description,
        analysis=analysis,  # This will now accept a dictionary
        is_final_meal=is_final_meal,
    )
    session.add(meal_log)
    session.commit()
    return meal_log


def get_meal_logs(
    session: Session,
    user: User,
    skip: int = 0,
    limit: int = 20,
    log_date: date | None = None,
) -> tuple[Sequence[MealLog], int]:
    query = select(MealLog).where(MealLog.user_id == user.id)

    if log_date:
        # Ensure the date comparison only considers the date part
        query = query.where(func.date(MealLog.created_at) == log_date)

    query = query.order_by(asc(MealLog.created_at))

    total = session.scalar(select(func.count()).select_from(query.subquery())) or 0
    meal_logs = session.scalars(query.offset(skip).limit(limit)).all()
    return meal_logs, total


def get_meal_log(session: Session, user_id: int, meal_log_id: int) -> MealLog | None:
    return session.scalar(
        select(MealLog).where(MealLog.id == meal_log_id, MealLog.user_id == user_id)
    )


def update_meal_log(
    session: Session, meal_log: MealLog, update_data: dict[str, Any]
) -> MealLog:
    for key, value in update_data.items():
        if hasattr(meal_log, key):
            setattr(meal_log, key, value)
    session.commit()
    return meal_log


def delete_meal_log(session: Session, meal_log: MealLog) -> None:
    session.delete(meal_log)
    session.commit()


# endregion MEAL_LOG


@lru_cache(maxsize=1000)
def get_cached_user(session: Session, id: int) -> User | None:
    return get_user(session, id)


@lru_cache(maxsize=100)
def get_cached_index_by_name(session: Session, name: str) -> BinaryData | None:
    return get_index_by_name(session, name)
