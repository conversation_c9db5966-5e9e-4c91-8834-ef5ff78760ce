from os import environ
from sqlalchemy import create_engine
from sqlalchemy.orm import Session, sessionmaker
from typing import Generator

SQLALCHEMY_DATABASE_URL = environ["DATABASE_URL"]
db_url = environ["DATABASE_URL"].replace("postgres://", "postgresql+psycopg2://")
engine = create_engine(db_url)
SessionLocal = sessionmaker(bind=engine)


def get_db_session() -> Generator[Session, None, None]:
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()
