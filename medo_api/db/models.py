import json
from datetime import datetime
from enum import Enum
from typing import Optional

from sqlalchemy import JSON, ForeignKey, LargeBinary, func
from sqlalchemy.dialects import postgresql
from sqlalchemy.orm import (
    DeclarativeBase,
    Mapped,
    MappedAsDataclass,
    mapped_column,
    relationship,
)

from ..schemas.meal_logs import MealLogResponse
from ..schemas.sense_score import CheckinReponse, PillarsResponse


class Pillar(str, Enum):
    nutrition = "nutrition"
    exercise = "exercise"
    emotionalWellbeing = "emotionalWellbeing"
    socialConnection = "socialConnection"
    sleep = "sleep"


class PillarMatchDB(str, Enum):
    emotionalWellbeing = "emotional_wellbeing"
    exercise = "exercise"
    nutrition = "nutrition"
    sleep = "sleep"
    socialConnection = "social_connection"


class Base(MappedAsDataclass, DeclarativeBase):
    """Base for SQLAlchemy models"""

    def __repr__(self) -> str:
        properties = self.__dict__.copy()
        if "_sa_instance_state" in properties:
            del properties["_sa_instance_state"]
        return f"{properties}"


class User(Base):
    __tablename__ = "user"
    id: Mapped[int] = mapped_column(init=False, primary_key=True)
    firebase_uid: Mapped[str] = mapped_column(index=True, unique=True)
    name: Mapped[Optional[str]]
    email: Mapped[Optional[str]]
    dob: Mapped[Optional[datetime]]
    gender: Mapped[Optional[str]]
    chat_histories: Mapped[list["ChatHistory"]] = relationship(
        "ChatHistory", back_populates="user", cascade="all, delete-orphan", init=False
    )
    initial_pillars: Mapped[Optional["InitialPillars"]] = relationship(
        back_populates="user", cascade="all, delete-orphan", init=False
    )
    checkins: Mapped[list["Checkin"]] = relationship(
        back_populates="user", cascade="all, delete-orphan", init=False
    )
    meal_logs: Mapped[list["MealLog"]] = relationship(
        "MealLog", back_populates="user", cascade="all, delete-orphan", init=False
    )
    profile: Mapped[Optional["UserProfile"]] = relationship(
        back_populates="user",
        uselist=False,
        cascade="all, delete-orphan",
        init=False,
    )
    created_at: Mapped[datetime] = mapped_column(default=func.now())
    updated_at: Mapped[datetime] = mapped_column(
        default=func.now(), onupdate=func.now()
    )


class ChatHistory(Base):
    __tablename__ = "chat_history"
    id: Mapped[int] = mapped_column(init=False, primary_key=True)
    question: Mapped[str]
    answer: Mapped[str]
    user_id: Mapped[int] = mapped_column(ForeignKey("user.id"), init=False, index=True)
    user: Mapped[Optional["User"]] = relationship(
        "User", back_populates="chat_histories"
    )
    created_at: Mapped[datetime] = mapped_column(default=func.now())

    def to_json(self):
        return {
            "createdAt": self.created_at,
            "question": self.question,
            "answer": self.answer,
        }


class BinaryData(Base):
    __tablename__ = "binary_data"
    id: Mapped[int] = mapped_column(init=False, primary_key=True)
    name: Mapped[str] = mapped_column(index=True, unique=True)
    contents: Mapped[bytes] = mapped_column(LargeBinary)
    created_at: Mapped[datetime] = mapped_column(default=func.now())
    updated_at: Mapped[datetime] = mapped_column(
        default=func.now(), onupdate=func.now()
    )


class InitialPillars(Base):
    __tablename__ = "initial_pillars"
    id: Mapped[int] = mapped_column(init=False, primary_key=True)
    user_id: Mapped[int] = mapped_column(ForeignKey("user.id"), init=False, index=True)
    user: Mapped["User"] = relationship(back_populates="initial_pillars")
    typeform_result: Mapped[dict] = mapped_column(JSON)
    sleep: Mapped[int]
    exercise: Mapped[int]
    nutrition: Mapped[int]
    social_connection: Mapped[int]
    emotional_wellbeing: Mapped[int]
    sense_score: Mapped[int]
    additional_data: Mapped[dict] = mapped_column(
        postgresql.JSONB, default_factory=dict
    )
    created_at: Mapped[datetime] = mapped_column(default=func.now())
    updated_at: Mapped[datetime] = mapped_column(
        default=func.now(), onupdate=func.now()
    )

    def to_json(self) -> PillarsResponse:
        return PillarsResponse(
            sleep=self.sleep,
            exercise=self.exercise,
            nutrition=self.nutrition,
            socialConnection=self.social_connection,
            emotionalWellbeing=self.emotional_wellbeing,
        )


class Checkin(Base):
    __tablename__ = "checkin"
    id: Mapped[int] = mapped_column(init=False, primary_key=True)
    user_id: Mapped[int] = mapped_column(ForeignKey("user.id"), init=False, index=True)
    user: Mapped["User"] = relationship(back_populates="checkins")
    nutrition: Mapped[Optional[str]]
    social_connection: Mapped[Optional[int]]
    emotional_wellbeing: Mapped[Optional[int]]
    created_at: Mapped[datetime] = mapped_column(default=func.now())
    updated_at: Mapped[datetime] = mapped_column(
        default=func.now(), onupdate=func.now()
    )

    def to_json(self) -> CheckinReponse:
        return CheckinReponse(
            nutrition=self.nutrition,
            emotionalWellbeing=self.emotional_wellbeing,
            socialConnection=self.social_connection,
            createdAt=self.created_at.isoformat(),
        )


class MealLog(Base):
    __tablename__ = "meal_log"
    id: Mapped[int] = mapped_column(init=False, primary_key=True)
    user_id: Mapped[int] = mapped_column(ForeignKey("user.id"), init=False, index=True)
    user: Mapped["User"] = relationship(back_populates="meal_logs")
    meal_type: Mapped[str]
    description: Mapped[str]
    analysis: Mapped[dict] = mapped_column(JSON)
    is_final_meal: Mapped[bool] = mapped_column(default=False)
    created_at: Mapped[datetime] = mapped_column(default=func.now())
    updated_at: Mapped[datetime] = mapped_column(
        default=func.now(), onupdate=func.now()
    )

    def to_json(self) -> MealLogResponse:
        return MealLogResponse(
            id=self.id,
            meal_type=self.meal_type,
            description=self.description,
            analysis=json.dumps(self.analysis),  # Convert dict to JSON string
            is_final_meal=self.is_final_meal,
            createdAt=self.created_at,
            updatedAt=self.updated_at,
        )


class UserProfile(Base):
    __tablename__ = "user_profile"
    id: Mapped[int] = mapped_column(init=False, primary_key=True)
    user_id: Mapped[int] = mapped_column(
        ForeignKey("user.id"), init=False, index=True, unique=True
    )
    user: Mapped["User"] = relationship(back_populates="profile")
    user_attributes: Mapped[list[dict]] = mapped_column(JSON)
    profile_bio_data: Mapped[str]
    created_at: Mapped[datetime] = mapped_column(default=func.now())
    updated_at: Mapped[datetime] = mapped_column(
        default=func.now(), onupdate=func.now()
    )
