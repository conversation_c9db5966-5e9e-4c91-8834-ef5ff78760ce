import asyncio
import logging
import os
import sys
from contextlib import asynccontextmanager
from datetime import datetime

import psutil
import structlog
from fastapi import FastAP<PERSON>, Request
from fastapi.middleware.cors import CORSMiddleware

from .routers.chat_router import chat_router
from .routers.meal_logs_router import meal_logs_router
from .routers.profile_router import profile_router
from .routers.recommendation_router import recommendation_router
from .routers.sense_score_router import sense_score_router
from .routers.support_router import support_router
from .utils.memory_monitor import MemoryMonitor

# Logging
logging.basicConfig(stream=sys.stdout, level=logging.DEBUG)
logger = logging.getLogger(__name__)


def setup_logging():
    structlog.configure(
        processors=[
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.processors.JSONRenderer(),
        ],
        wrapper_class=structlog.make_filtering_bound_logger(logging.INFO),
        context_class=dict,
        logger_factory=structlog.PrintLoggerFactory(),
        cache_logger_on_first_use=True,
    )


async def periodic_memory_check():
    while True:
        process = psutil.Process()
        memory_info = process.memory_info()

        logger.info(
            "Memory Check - RSS: %.2f MB, VMS: %.2f MB, Percent: %.2f%%, Time: %s",
            memory_info.rss / 1024 / 1024,
            memory_info.vms / 1024 / 1024,
            process.memory_percent(),
            datetime.now().isoformat(),
        )

        await asyncio.sleep(300)  # Check every 5 minutes


@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    Check for required environment variables here. Environment variables
    that are required must be declared so that the app fails on startup
    if the do not exist.
    """
    required_env_vars = [
        "OPENAI_API_KEY",
        "ANTHROPIC_API_KEY",
        "TYPEFORM_API_KEY",
        "LANGCHAIN_API_KEY",
        "DATABASE_URL",
        "FIREBASE_PROJECT_ID",
        "FIREBASE_PRIVATE_KEY_ID",
        "FIREBASE_PRIVATE_KEY",
        "FIREBASE_CLIENT_EMAIL",
        "FIREBASE_CLIENT_ID",
        "FIREBASE_CLIENT_x509_CERT_URL",
        "BINARY_DATA_NAME_TO_USE",
        "REDISCLOUD_URL",
    ]
    missing_vars = []
    for required in required_env_vars:
        if required not in os.environ:
            missing_vars.append(required)
    if len(missing_vars) > 0:
        raise ValueError(
            f"Missing required environment variables: {', '.join(missing_vars)}"
        )

    task = asyncio.create_task(periodic_memory_check())

    yield

    task.cancel()
    try:
        await task
    except asyncio.CancelledError:
        pass
    # Clean up - nothing


# Initialise the FastAPI app
app = FastAPI(lifespan=lifespan)


origins = []
if "ALLOWED_ORIGIN" in os.environ:
    origins.append(os.environ["ALLOWED_ORIGIN"])


logger.info(f"ALLOWED ORIGINS: {origins}")
app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(support_router)
app.include_router(chat_router)
app.include_router(sense_score_router)
app.include_router(profile_router)
app.include_router(recommendation_router)
app.include_router(meal_logs_router)


@app.middleware("http")
async def monitor_memory_usage(request: Request, call_next):
    process = psutil.Process()
    memory_before = process.memory_info().rss / 1024 / 1024

    response = await call_next(request)

    memory_after = process.memory_info().rss / 1024 / 1024
    logger.info(
        f"Memory Usage - Before: {memory_before:.2f}MB, After: {memory_after:.2f}MB"
    )

    return response
