import gc
import logging
import os
from enum import Enum
from typing import Optional

from langchain.callbacks.tracers import <PERSON><PERSON>hainTrace<PERSON>
from langchain_anthropic import Cha<PERSON><PERSON><PERSON>hropic
from langchain_community.vectorstores.faiss import FAISS
from langchain_core.output_parsers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ars<PERSON>, StrOutputParser
from langchain_core.prompts import Chat<PERSON>rompt<PERSON>emplate, PromptTemplate
from langchain_core.runnables import RunnablePassthrough
from langchain_openai import ChatOpenAI, OpenAIEmbeddings
from pydantic import SecretStr

from medo_api.cache.redis_cache import RedisCache

logger = logging.getLogger(__name__)


class EmbeddingName(Enum):
    OPENAI_TEXT_EMBEDDING_ADA_002 = "text-embedding-ada-002"
    OPENAI_TEXT_EMBEDDING_3_SMALL = "text-embedding-3-small"
    OPENAI_TEXT_EMBEDDING_3_LARGE = "text-embedding-3-large"


class AnthropicModel(Enum):
    CLAUDE_INSTANCE_1_2 = "claude-instant-1.2"
    CLAUDE_2_0 = "claude-2.0"
    CLAUDE_2_1 = "claude-2.1"
    CLAUDE_3_HAIKU_20240307 = "claude-3-haiku-20240307"
    CLAUDE_3_SONNET_20240229 = "claude-3-sonnet-20240229"
    CLAUDE_3_5_SONNET_20240620 = "claude-3-5-sonnet-20240620"
    CLAUDE_3_5_SONNET_20241022 = "claude-3-5-sonnet-20241022"
    CLAUSE_3_OPUS_20240229 = "claude-3-opus-20240229"


class OpenAIModel(Enum):
    GPT_4O = "gpt-4o"
    GPT_4O_MINI = "gpt-4o-mini"


LANGSMITH_TRACING = os.environ.get("LANGSMITH_TRACING", False)
LLM_MODEL = os.environ.get("LLM_MODEL", AnthropicModel.CLAUDE_3_HAIKU_20240307.value)
LLM_TIMEOUT = float(os.environ.get("LLM_TIMEOUT", 90.0))


def get_llm_model_name() -> AnthropicModel | OpenAIModel:
    match LLM_MODEL:
        case AnthropicModel.CLAUDE_3_HAIKU_20240307.value:
            return AnthropicModel.CLAUDE_3_HAIKU_20240307
        case AnthropicModel.CLAUDE_3_5_SONNET_20241022.value:
            return AnthropicModel.CLAUDE_3_5_SONNET_20241022
        case AnthropicModel.CLAUDE_3_5_SONNET_20240620.value:
            return AnthropicModel.CLAUDE_3_5_SONNET_20240620
        case OpenAIModel.GPT_4O.value:
            return OpenAIModel.GPT_4O
        case OpenAIModel.GPT_4O_MINI.value:
            return OpenAIModel.GPT_4O_MINI
        case _:
            raise ValueError(f"Unsupported LLM model name: {LLM_MODEL}")


def get_llm_model() -> ChatAnthropic | ChatOpenAI:
    llm_model_name = get_llm_model_name()
    if isinstance(llm_model_name, AnthropicModel):
        logger.info(f"Using Anthropic model: {llm_model_name.value}")
        return ChatAnthropic(
            model_name=llm_model_name.value,
            api_key=SecretStr(os.environ["ANTHROPIC_API_KEY"]),
            timeout=LLM_TIMEOUT,
            stop=None,
        )
    elif isinstance(llm_model_name, OpenAIModel):
        logger.info(f"Using OpenAI model: {llm_model_name.value}")
        return ChatOpenAI(
            model=llm_model_name.value,
            api_key=SecretStr(os.environ["OPENAI_API_KEY"]),
            timeout=LLM_TIMEOUT,
        )
    else:
        raise ValueError(f"Unsupported LLM model: {llm_model_name}")


class RAGProcessor:

    def __init__(
        self,
        company_name: str,
        user_name: str,
        instructions: str,
        additional_user_context: str,
        index_binary_data: bytes,
        enable_langsmith_tracer: bool = bool(LANGSMITH_TRACING),
        openai_api_key: str | None = None,
        anthropic_api_key: str | None = None,
        langchain_api_key: str | None = None,
        output_parser: Optional[JsonOutputParser] = None,
        chunk_size: int = 1024 * 1024,  # 1MB chunks
        redis_cache: Optional[RedisCache] = None,
    ):
        # State
        self.company_name = company_name
        self.user_name = user_name
        self.instructions = instructions
        self.additional_user_context = additional_user_context
        self.openai_api_key = openai_api_key or os.environ["OPENAI_API_KEY"]
        self.anthropic_api_key = anthropic_api_key or os.environ["ANTHROPIC_API_KEY"]
        self.enable_langsmith_tracer = enable_langsmith_tracer
        self.langchain_api_key = langchain_api_key or os.environ["LANGCHAIN_API_KEY"]
        self.output_parser = output_parser
        self.chunk_size = chunk_size
        self.redis_cache = redis_cache

        # Create the retriever
        embedding_model_name = os.getenv(
            "EMBEDDING_MODEL_NAME", EmbeddingName.OPENAI_TEXT_EMBEDDING_3_LARGE.value
        )
        self.embedding_model = OpenAIEmbeddings(
            api_key=SecretStr(self.openai_api_key),
            model=embedding_model_name,
            disallowed_special=(),
        )
        index = FAISS.deserialize_from_bytes(
            serialized=index_binary_data,
            embeddings=self.embedding_model,
            allow_dangerous_deserialization=True,
        )
        self.retriever = index.as_retriever()

        # Determine the prompt template to use
        try:
            if self.output_parser:
                self.prompt = PromptTemplate(
                    template=instructions
                    + """
                        User's Health Profile: {additional_user_context}
                        Context from knowledge base: {context}
                        Question: {question}
                        {format_instructions}
                    """,
                    input_variables=["additional_user_context", "context", "question"],
                    partial_variables={
                        "format_instructions": self.output_parser.get_format_instructions()
                    },
                )
            else:
                logger.debug("Template before processing: %s", instructions)
                escaped_instructions = instructions.replace("[", "{{[}}").replace(
                    "]", "{{]}}"
                )
                self.prompt = ChatPromptTemplate.from_template(
                    escaped_instructions
                    + """
                        User's Health Profile: {additional_user_context}
                        Context from knowledge base: {context}
                        Question: {question}
                        {format_instructions}
                    """
                )
        except ValueError as ve:
            logger.error("Template parsing error: %s", str(ve))
            raise ValueError(f"Error parsing template: {str(ve)}")

    def load_index_chunks(self, binary_data: bytes):
        for i in range(0, len(binary_data), self.chunk_size):
            chunk = binary_data[i : i + self.chunk_size]
            yield chunk

    async def execute(self, question: str) -> str:
        llm_model = get_llm_model()

        # Try to get cached embeddings first
        cache_key = f"embedding:{self.company_name}:{hash(question)}"
        cached_result = None
        if self.redis_cache:
            cached_result = self.redis_cache.get(cache_key)
            if cached_result:
                logger.info("Using cached embedding result")
                return cached_result

        # Adjust the chain based on the presence of output_parser
        if self.output_parser:
            chain = (
                {
                    "context": self.retriever,
                    "question": RunnablePassthrough(),
                    "additional_user_context": lambda _: self.additional_user_context,
                    "format_instructions": lambda _: self.output_parser.get_format_instructions(),
                }
                | self.prompt
                | llm_model
                | self.output_parser
            ).with_config({"run_name": self.company_name + "'s run"})
        else:
            chain = (
                {
                    "context": self.retriever,
                    "question": RunnablePassthrough(),
                    "additional_user_context": lambda _: self.additional_user_context,
                    "format_instructions": lambda _: "",
                }
                | self.prompt
                | llm_model
                | StrOutputParser()
            ).with_config({"run_name": self.company_name + "'s run"})

        callbacks = []
        if self.enable_langsmith_tracer:
            callbacks.append(LangChainTracer(project_name=self.company_name))

        try:
            answer = await chain.ainvoke(
                question,
                config={
                    "callbacks": callbacks,
                    "tags": [self.company_name],
                    "metadata": {
                        "company_name": self.company_name,
                        "user": self.user_name,
                        "retriever": type(self.retriever).__name__,
                    },
                },
            )

            # Cache the result
            if self.redis_cache:
                self.redis_cache.set(cache_key, answer, expire=3600)  # Cache for 1 hour

            return answer
        finally:
            # Cleanup
            self.cleanup()

    def cleanup(self):
        """Clean up memory after processing"""
        if hasattr(self, "retriever"):
            del self.retriever
        if hasattr(self, "prompt"):
            del self.prompt
        if hasattr(self, "embedding_model"):
            del self.embedding_model
        gc.collect()

    @classmethod
    def force_cleanup(cls):
        """Force cleanup of any lingering resources"""
        gc.collect()
        # Force a full collection
        gc.collect(2)
        # Disable gc temporarily to ensure all objects are collected
        gc.disable()
        gc.enable()
