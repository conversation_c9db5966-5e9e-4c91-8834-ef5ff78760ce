import logging
import os
from typing import Annotated
from fastapi import Depends, HTTPException, status
from fastapi.security import OAuth2PasswordBearer
from sqlalchemy.orm import Session
from firebase_admin import credentials, initialize_app, auth
from firebase_admin._auth_utils import InvalidIdTokenError
from ..db.engine import get_db_session
from ..db.crud import (
    get_user_by_firebase_uid,
    create_user,
)
from ..db.models import User

# Logging
logger = logging.getLogger(__name__)

# Config
FIREBASE_PROJECT_ID = os.environ["FIREBASE_PROJECT_ID"]
FIREBASE_PRIVATE_KEY = (
    os.environ["FIREBASE_PRIVATE_KEY"].encode().decode("unicode-escape")
)  # unescape line breaks in private key
FIREBASE_PRIVATE_KEY_ID = os.environ["FIREBASE_PRIVATE_KEY_ID"]
FIREBASE_CLIENT_EMAIL = os.environ["FIREBASE_CLIENT_EMAIL"]
FIREBASE_CLIENT_ID = os.environ["FIREBASE_CLIENT_ID"]
FIREBASE_CLIENT_EMAIL = os.environ["FIREBASE_CLIENT_EMAIL"]
FIREBASE_CLIENT_x509_CERT_URL = os.environ["FIREBASE_CLIENT_x509_CERT_URL"]

# Initialise
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="token")
cred = credentials.Certificate(
    {
        "type": "service_account",
        "project_id": FIREBASE_PROJECT_ID,
        "private_key_id": FIREBASE_PRIVATE_KEY_ID,
        "private_key": FIREBASE_PRIVATE_KEY,
        "client_email": FIREBASE_CLIENT_EMAIL,
        "client_id": FIREBASE_CLIENT_ID,
        "auth_uri": "https://accounts.google.com/o/oauth2/auth",
        "token_uri": "https://oauth2.googleapis.com/token",
        "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs",
        "client_x509_cert_url": FIREBASE_CLIENT_x509_CERT_URL,
        "universe_domain": "googleapis.com",
    }
)
firebase_app = initialize_app(cred)


async def get_current_user(
    token: Annotated[str, Depends(oauth2_scheme)],
    session: Session = Depends(get_db_session),
) -> User:
    """
    Get the current user from the UID in the Firebase JWT token
    """
    try:
        logger.debug(f"token: {token}")
        decoded_token = auth.verify_id_token(token)
        logger.info(f"decoded token: {decoded_token}")
        user = get_user_by_firebase_uid(
            session=session, firebase_uid=decoded_token["uid"]
        )
        if not user:
            name = decoded_token["name"] if "name" in decoded_token else None
            email = decoded_token["email"] if "email" in decoded_token else None
            user = create_user(
                session=session,
                firebase_uid=decoded_token["uid"],
                name=name,
                email=email,
                dob=None,
                gender=None,
            )
        return user
    except InvalidIdTokenError as error:
        logger.exception(error)
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )
    except ValueError as error:
        logger.exception(error)
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid token (possibly empty)",
            headers={"WWW-Authenticate": "Bearer"},
        )
