import gc
import logging
import os
from datetime import datetime, timed<PERSON>ta
from typing import Annotated, List, Optional

from fastapi import API<PERSON><PERSON><PERSON>, Depen<PERSON>, <PERSON>er, HTTPException, Query, status
from pydantic import BaseModel, Field
from sqlalchemy import select
from sqlalchemy.orm import Session

from ..ai.recommendations import get_additional_user_pillar_context
from ..db.crud import create_chat_history, delete_chat_history_range
from ..db.engine import get_db_session
from ..db.models import BinaryData, ChatHistory, InitialPillars, PillarMatchDB, User
from ..rag_lib import RAGProcessor
from ..routers.router_utils import get_recent_meal_logs
from ..utils.memory_monitor import MemoryMonitor
from ..utils.settings import Settings
from ..utils.text_sanitizer import sanitize_input
from .auth import get_current_user
from .sense_score_router import get_sense_score_api

# Logging
logger = logging.getLogger(__name__)

# Config
ALGORITHM = "HS256"
OPENAI_API_KEY = os.environ["OPENAI_API_KEY"]
ANTHROPIC_API_KEY = os.environ["ANTHROPIC_API_KEY"]
BINARY_DATA_NAME = os.environ["BINARY_DATA_NAME_TO_USE"]


# Data types
class Question(BaseModel):
    question: str = Field(..., description="The question to be answered")

    model_config = {
        "json_schema_extra": {
            "example": {"question": "What are the benefits of a Mediterranean diet?"}
        }
    }


class ChatHistoryEntry(BaseModel):
    createdAt: datetime = Field(
        ..., description="Timestamp of when the chat entry was created"
    )
    question: str = Field(..., description="The question asked by the user")
    answer: str = Field(..., description="The answer provided by the system")

    model_config = {
        "json_schema_extra": {
            "example": {
                "createdAt": "2024-03-15T14:30:00Z",
                "question": "What are the benefits of a Mediterranean diet?",
                "answer": """The Mediterranean diet has numerous benefits, including
                improved heart health, reduced risk of chronic diseases, and potential
                weight management advantages.""",
            }
        }
    }


class AnswerResponse(BaseModel):
    answer: str = Field(..., description="The answer to the user's question")

    model_config = {
        "json_schema_extra": {
            "example": {
                "answer": """The Mediterranean diet offers several benefits:
                • Improved heart health
                • Reduced risk of chronic diseases
                • Potential weight management advantages
                • Rich in antioxidants and anti-inflammatory foods
                • Promotes longevity and overall well-being
                How might you incorporate some Mediterranean diet principles into your
                meals this week?"""
            }
        }
    }


class IndexNotInDBException(Exception):
    pass


# Initialise
chat_router = APIRouter()


def get_other_user_data(session: Session, user: User, other_fields: list[str]) -> str:
    user_context = """
    Other user information:
    """
    initial_pillars = session.scalar(
        select(InitialPillars).where(InitialPillars.user_id == user.id)
    )
    if initial_pillars and initial_pillars.additional_data:
        for field in other_fields:
            field_info = f"""
            {field}:
            """
            for question, answer in initial_pillars.additional_data.get(
                field, {}
            ).items():
                field_info += f"{question}: {answer}\n"
            user_context += field_info
    return user_context


def get_additional_user_context(session: Session, user: User) -> str:
    user_context = """
    User's additional information:
    """
    other_fields = [
        "other",
    ]
    print("IN CHAT ROUTER GET ADDITIONAL USER CONTEXT")
    pillars = [
        PillarMatchDB.emotionalWellbeing.value,
        PillarMatchDB.exercise.value,
        PillarMatchDB.nutrition.value,
        PillarMatchDB.socialConnection,
        PillarMatchDB.sleep.value,
    ]

    for pillar in pillars:
        user_context += get_additional_user_pillar_context(
            session=session, user=user, pillar_db_name=pillar
        )

    other_user_context = get_other_user_data(
        session=session, user=user, other_fields=other_fields
    )
    return user_context + other_user_context


async def execute_rag(
    session: Session,
    user: User,
    question: str,
    page: int = 1,
    page_size: int = 20,
    len_of_chat_history: int = 5,
    current_time: Optional[datetime] = None,
    utc_offset: Optional[float] = None,
    meal_logs_days: int = 14,
) -> str:
    processor = None
    try:
        MemoryMonitor.log_memory_usage()
        row = session.scalar(
            select(BinaryData).where(BinaryData.name == BINARY_DATA_NAME)
        )
        if not row:
            raise IndexNotInDBException
        index_binary_data = row.contents

        # Clear any previous large objects
        gc.collect()

        additional_user_context = get_additional_user_context(session, user)
        users_profile_bio = user.profile.profile_bio_data if user.profile else ""
        user_name = user.name.split(" ")[0] if user.name else "user"

        # Get SENSE score and pillars
        sense_score_response = await get_sense_score_api(user=user, session=session)
        sense_score = sense_score_response.senseScore
        pillars = sense_score_response.pillars.dict()

        # Use pagination for chat history
        offset = (page - 1) * page_size
        recent_history = session.scalars(
            select(ChatHistory)
            .where(ChatHistory.user_id == user.id)
            .order_by(ChatHistory.created_at.desc())
            .offset(offset)
            .limit(page_size)
        ).all()

        # Format chat history with adjusted timestamps
        chat_history = "\n".join(
            [
                f"User: {history.question}\n" f"Medo: {history.answer}"
                for history in reversed(recent_history)
            ]
        )

        # Include current time in the prompt only if provided
        time_context = (
            f"Current local time: {current_time.strftime('%Y-%m-%d %H:%M:%S')}"
            if current_time
            else ""
        )

        meal_logs = get_recent_meal_logs(session, user.id, meal_logs_days, utc_offset)

        # Include meal logs in the prompt
        meal_logs_context = f"""Recent meal logs,
            You must consider the time of day and date in your responses:
            {meal_logs}
        """

        instructions = f"""
        You are Medo, a personalised health coach and longevity expert helping users
        achieve health goals based on their specific profile and health data. Follow these
        guidelines:
        - Provide concise, clear answers using available resources and your expertise
            that is not just general advice from the resources.
        - Do not solely rely on the resources, use your expertise as well.
        - Do not exceed 600 characters.
        - Incorporate the user's health insights, quiz answers, and goals.
        - Prioritize advice based on the user's weakest health area.
        - Focus on one actionable item per interaction, then follow up on it.
        - Ask follow-up questions to gather more context and encourage engagement.
        - You don't always have to give advice, sometimes just acknowledging the user's
        situation is enough or leading the conversation forward.
        - You must take into account all factors in your responses, time, meal logs,
        and other user information in your responses.
        - Consider the time of day, user's location, and other factors when making
        suggestions:
            - For example if the user says they travelled to a conference in New York, and
            asked about nutrition, you might suggest foods that are common in New York.
            - Another example might be if the user is a busy professional, you might
            suggest quick and easy recipes or exercises they can do at their desk.

        {time_context}

        User's SENSE Score: {sense_score}
        User's Pillar Scores:
        - Sleep: {pillars['sleep']}
        - Exercise: {pillars['exercise']}
        - Nutrition: {pillars['nutrition']}
        - Social Connection: {pillars['socialConnection']}
        - Emotional Wellbeing: {pillars['emotionalWellbeing']}

        When considering the user's SENSE score and pillar scores:
        - Use this information as a general guide, not as absolute truth.
        - Prioritize the user's recent interactions and stated goals over these scores.
        - If asked about the scores, provide the information but emphasize that they are based on the initial assessment and may not reflect current status.
        - Encourage focus on recent progress and current goals rather than fixating on the scores.

        - Never start responses with phrases like "Medo, the knowledgeable and compassionate
        longevity assistant, responds:", "Based on your profile" or any similar
        introductions.
        - Avoid responses like:
          - "Let's focus on improving your sleep, as that seems to be one of your weaker health areas based on your profile"
        - Use bullet points when appropriate for readability.
        - Be empathetic and maintain a polite and encouraging tone.
        - Ensure accuracy and reliability of information.
        - If a question is unclear or potentially out of scope on the topic of health and
        longevity (specifically around the five pillars relating to your SENSE score =
        Sleep, Exercise, Nutrition, Social connectivity, and Emotional wellness):
            1. Ask 1-2 clarifying questions to better understand the user's intent.
            2. If clarification helps, provide an answer based on the additional
            information.
            3. If still uncertain after clarification, or if the topic is completely
            unrelated to health and longevity, use this fallback:
            "I am not sure I am in the best position to be able to address this question.
            Would you like me to connect you with a Medo expert via email who might be able
            to assist you better?"
        - End responses with engaging, specific questions or offers that:
            1. Relate directly to the topic just discussed
            2. Encourage the user to reflect on applying the information
            3. Offer to explore related health pillars
            4. Prompt the user to set small, achievable goals
            5. Provide options for further discussion or clarification
            Examples:
                "How might you incorporate this into your daily routine? Is there a specific
                aspect you'd like to explore further?"
                "Would you like to discuss how this connects to [another health pillar], or
                is there a different area of your health you're curious about?"
                "What small step could you take this week to improve your [relevant
                health aspect]? I'm here to help brainstorm ideas if you'd like."
            Avoid generic closings like "I'm happy to discuss this further." Always aim to
            guide the conversation forward in a personalized, constructive manner.

            Recent chat history:
            {chat_history}

            Based on this context, the chat history, and the current time, provide a conversational and
            personalized response. Remember to:
            - Address any follow-ups from previous interactions
            - Maintain continuity in the conversation
            - Provide one clear, actionable item related to the current topic
            - Ask a relevant follow-up question to keep the conversation going
            - Consider the time elapsed since previous interactions when giving advice

            {Settings.USERS_PROFILE_INSTRUCTIONS}
            This is the user's profile bio, use it to personalise your response:
            {users_profile_bio}
            {meal_logs_context}
        """  # noqa: E501
        logger.debug(f"Generated instructions template: {instructions}")
        processor = RAGProcessor(
            company_name="medo",
            user_name=user_name,
            instructions=instructions,
            additional_user_context=additional_user_context,
            index_binary_data=index_binary_data,
        )

        result = await processor.execute(question=question)
        return result

    except ValueError as ve:
        logger.error(f"Template formatting error: {str(ve)}")
        # Return a graceful fallback response
        return "I apologize, but I encountered an error processing your request. Please try again or rephrase your question."  # noqa: E501
    except Exception as e:
        logger.error(f"Unexpected error in execute_rag: {str(e)}")
        raise
    finally:
        if processor:
            processor.cleanup()
            del processor
        # Force cleanup of any remaining objects
        gc.collect()
        MemoryMonitor.log_memory_usage()


@chat_router.post(
    "/ask_question",
    response_model=AnswerResponse,
    tags=["Chat"],
    summary="Ask a question",
)
async def ask_question(
    question: Question,
    user: Annotated[User, Depends(get_current_user)],
    session: Annotated[Session, Depends(get_db_session)],
    len_of_chat_history: int = Query(default=5, le=10),
    utc_offset: Optional[float] = Header(
        None, description="Hours offset from UTC (e.g., -5.0 for EST)"
    ),
    meal_logs_days: int = Query(default=14, le=30),
):
    """
    Ask a question to the AI health coach.

    - **question**: The question to be answered by the AI
    - **len_of_chat_history**: Optional. The number of chat history messages to include in the prompt (default 5, max 10)
    - **utc_offset**: Optional. The client's UTC offset in hours (e.g., -5.0 for EST, 5.5 for IST)
    - **meal_logs_days**: Optional. The number of days of meal logs to include in the prompt (default 14, max 30)

    If a UTC offset is provided, it will be used to give time-aware responses.
    If no offset is provided, the response will not include time-specific information.

    Returns:
    - An object containing the answer to the user's question

    ```
    Example response:
        {
            "answer": "The Mediterranean diet offers several benefits:
            • Improved heart health
            • Reduced risk of chronic diseases
            • Promotes longevity and overall well-being
            How might you incorporate some Mediterranean diet principles into your
            meals this week?"
        }
    ```
    """  # noqa: E501
    current_time = None
    if utc_offset is not None:
        current_time = datetime.utcnow() + timedelta(hours=utc_offset)

    try:
        # Sanitize the question
        sanitized_question = sanitize_input(question.question)

        answer = await execute_rag(
            session=session,
            user=user,
            question=sanitized_question,
            len_of_chat_history=len_of_chat_history,
            current_time=current_time,
            utc_offset=utc_offset,
            meal_logs_days=meal_logs_days,
        )

        create_chat_history(
            session=session,
            user=user,
            question=question.question,  # Store original question
            answer=answer,
        )

        return AnswerResponse(answer=answer)

    except ValueError as ve:
        logger.error(f"Template formatting error: {str(ve)}")
        # Force cleanup of RAG processor resources
        RAGProcessor.force_cleanup()
        return AnswerResponse(
            answer="I apologize, but I encountered an error processing your request. Please try again or rephrase your question."
        )
    except Exception as e:
        logger.error(f"Unexpected error in ask_question: {str(e)}")
        raise


@chat_router.get(
    "/chat_history",
    response_model=List[ChatHistoryEntry],
    tags=["Chat"],
    summary="Get chat history",
)
async def get_chat_history(
    user: Annotated[User, Depends(get_current_user)],
    session: Annotated[Session, Depends(get_db_session)],
):
    """
    Retrieve the chat history for the authenticated user.

    - **user**: The authenticated user (automatically injected)
    - **session**: The database session (automatically injected)

    Returns:
    - A list of ChatHistoryEntry objects, each containing the creation timestamp,
    question, and answer

    ```
    Example response:
        [
            {
                "createdAt": "2024-03-15T14:30:00Z",
                "question": "What are the benefits of a Mediterranean diet?",
                "answer": "The Mediterranean diet offers several benefits:
                • Improved heart health
                • Reduced risk of chronic diseases
                • Potential weight management advantages
                • Rich in antioxidants and anti-inflammatory foods
                • Promotes longevity and overall well-being
                How might you incorporate some Mediterranean diet principles into your
                meals this week?"
            },
            {
                "createdAt": "2024-03-15T14:35:00Z",
                "question": "How can I improve my sleep quality?",
                "answer": "To improve sleep quality, consider these tips:
                • Maintain a consistent sleep schedule
                • Create a relaxing bedtime routine
                • Ensure your bedroom is dark, quiet, and cool
                • Limit screen time before bed
                • Avoid caffeine and large meals close to bedtime
                Which of these strategies do you think would be most helpful for you to
                try first?"
            }
        ]
    ```
    """
    return [
        ChatHistoryEntry(
            createdAt=history.created_at,
            question=history.question,
            answer=history.answer,
        )
        for history in user.chat_histories
    ]


@chat_router.delete(
    "/chat_history/{days}",
    status_code=status.HTTP_204_NO_CONTENT,
    tags=["Chat"],
    summary="[Testing Only] Delete chat history",
)
async def delete_chat_history(
    days: int,
    user: Annotated[User, Depends(get_current_user)],
    session: Annotated[Session, Depends(get_db_session)],
):
    """
    [Testing Only] Delete chat history older than specified number of days.

    - **days**: Number of days of chat history to keep (deletes everything older)

    Example:
    - If days = 2, keeps last 2 days of chat history and deletes everything older
    - If days = 0, deletes all chat history
    - If days = 7, keeps last week of chat history and deletes older entries

    Returns:
    - 204 No Content if deletion was successful

    Example request:
    ```
    DELETE /chat_history/7
    ```

    Example response:
    ```
    204 No Content
    ```
    """
    try:
        cutoff_date = datetime.utcnow() - timedelta(days=days)
        # Delete chat history older than cutoff date
        session.query(ChatHistory).where(
            ChatHistory.user_id == user.id, ChatHistory.created_at < cutoff_date
        ).delete(synchronize_session=False)
        session.commit()
        return None
    except Exception as e:
        logger.error(f"Error deleting chat history: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete chat history",
        ) from e


@chat_router.delete(
    "/chat_history/range/{days_ago_start}/{days_ago_end}",
    status_code=status.HTTP_200_OK,
    tags=["Chat"],
    summary="[Testing Only] Delete chat history within a date range",
)
async def delete_chat_history_range_in_route(
    days_ago_start: int,
    days_ago_end: int,
    user: Annotated[User, Depends(get_current_user)],
    session: Annotated[Session, Depends(get_db_session)],
):
    """
    [Testing Only] Delete chat history within a specific date range, keeping entries outside that range.

    - **days_ago_start**: Start of range (more recent date)
    - **days_ago_end**: End of range (older date)

    Example:
    - If days_ago_start = 2 and days_ago_end = 5:
        - Keeps entries from today and yesterday
        - Deletes entries from 2-5 days ago
        - Keeps entries older than 5 days

    Returns:
    - Number of records deleted

    Example request:
    ```
    DELETE /chat_history/range/2/5
    ```

    Example response:
    ```
    {
        "deleted_count": 10
    }
    ```
    """
    try:
        if days_ago_start >= days_ago_end:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Start days must be less than end days",
            )

        deleted_count = delete_chat_history_range(
            session=session,
            user=user,
            days_ago_start=days_ago_start,
            days_ago_end=days_ago_end,
        )

        return {"deleted_count": deleted_count}

    except Exception as e:
        logger.error(f"Error deleting chat history range: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete chat history",
        )
