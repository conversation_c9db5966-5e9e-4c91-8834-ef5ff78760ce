# medo_api/routers/meal_logs_router.py

import logging
from datetime import date
from math import ceil
from typing import Annotated

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy import func, select
from sqlalchemy.orm import Session

from ..ai.meal_analysis import process_meal_log
from ..ai.recommendations import invalidate_user_caches
from ..db.crud import create_meal_log as crud_create_meal_log
from ..db.crud import delete_meal_log as crud_delete_meal_log
from ..db.crud import get_meal_log as crud_get_meal_log
from ..db.crud import update_meal_log as crud_update_meal_log
from ..db.engine import get_db_session
from ..db.models import MealLog as MealLogModel
from ..db.models import User
from ..schemas.meal_logs import MealLog as MealLogRequest
from ..schemas.meal_logs import MealLogResponse
from ..schemas.pagination import PaginatedResponse
from .auth import get_current_user

# Logging
logger = logging.getLogger(__name__)

# Initialize
meal_logs_router = APIRouter()


def meal_log_to_dict(meal_log):
    return {
        "id": meal_log.id,
        "meal_type": meal_log.meal_type,
        "description": meal_log.description,
        "analysis": meal_log.analysis,
        "is_final_meal": meal_log.is_final_meal,
        "createdAt": meal_log.created_at.isoformat(),
        "updatedAt": meal_log.updated_at.isoformat(),
    }


@meal_logs_router.post(
    "/meal_logs",
    response_model=MealLogResponse,
    tags=["Meal Logs"],
    summary="Create a new meal log",
)
async def create_meal_log(
    meal_log: MealLogRequest,
    user: Annotated[User, Depends(get_current_user)],
    session: Annotated[Session, Depends(get_db_session)],
):
    """
    Create a new meal log, process it using AI, save to history & return analysis.

    - **meal_log**: The meal log data to be created
    - **user**: The authenticated user (automatically injected)
    - **session**: The database session (automatically injected)

    Returns:
    - The created meal log with AI analysis


    Example request:
    ```
    {
        "meal_type": "lunch",
        "description": "Grilled chicken salad with mixed greens, tomatoes, and balsamic vinaigrette",
        "is_final_meal": false
    }
    ```

    Example response:
    ```
    {
        "id": 1,
        "meal_type": "lunch",
        "description": "Grilled chicken salad with mixed greens, tomatoes, and balsamic vinaigrette",
        "analysis": {
            "score": 5,
            "summary": "Excellent meal choice. The addition of avocado provides healthy fats, enhancing nutrient absorption and promoting satiety.",
            "recommendations": "Consider adding a portion of whole grains for a balanced meal.",
        },
        "is_final_meal": false,
        "createdAt": "2024-03-16T12:30:00Z",
        "updatedAt": "2024-03-16T12:30:00Z"
    }
    ```
    """  # noqa: E501
    try:
        analysis_result = await process_meal_log(
            session=session, user=user, meal_log=meal_log
        )
        db_meal_log = crud_create_meal_log(
            session=session,
            user=user,
            meal_type=meal_log.meal_type,
            description=meal_log.description,
            analysis=analysis_result.analysis,
            is_final_meal=meal_log.is_final_meal,
        )
        # Invalidate nutrition-specific caches
        await invalidate_user_caches(user.id, invalidate_nutrition=True)

        return MealLogResponse(**meal_log_to_dict(db_meal_log))
    except Exception as e:
        logger.exception(f"Error creating meal log: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@meal_logs_router.get(
    "/meal_logs",
    response_model=PaginatedResponse[MealLogResponse],
    tags=["Meal Logs"],
    summary="Get all meal logs",
)
async def get_meal_logs(
    user: Annotated[User, Depends(get_current_user)],
    session: Annotated[Session, Depends(get_db_session)],
    page: int = 1,
    size: int = 20,
    log_date: date | None = None,
    sort_order: str = "desc",  # sorting order
    meal_type: str | None = None,  # filtering by meal type
    is_final_meal: bool | None = None,  # filtering by final meal
    num_logs: int | None = None,  # number of logs to return
):
    """
    Get all meal logs for the user with pagination, sorting, and optional filtering.

    - **page**: The page number for pagination (default: 1)
    - **size**: The number of items per page (default: 20)
    - **log_date**: Optional date filter for meal logs
    - **sort_order**: Order of sorting, either 'asc' or 'desc' (default: 'desc')
    - **meal_type**: Filter by meal type (optional)
    - **is_final_meal**: Filter by final meal status (optional)
    - **num_logs**: Number of logs to return (optional)

    Returns:
    - A paginated response containing meal logs


    Example response:
    ```
    {
        "items": [
            {
                "id": 1,
                "meal_type": "lunch",
                "description": "Grilled chicken salad with mixed greens",
                "analysis": {
                    "score": 4,
                    "summary": "Nutritious meal with good protein content...",
                    "recommendations": "Consider adding a portion of whole grains for a balanced meal.",
                },
                "is_final_meal": false,
                "createdAt": "2024-03-16T12:30:00Z",
                "updatedAt": "2024-03-16T12:30:00Z"
            },
            {
                "id": 2,
                "meal_type": "dinner",
                "description": "Baked salmon with roasted vegetables",
                "analysis": {
                    "score": 5,
                    "summary": "Excellent source of omega-3 fatty acids...",
                    "recommendations": "Consider adding a portion of whole grains for a balanced meal.",
                },
                "is_final_meal": true,
                "createdAt": "2024-03-16T19:00:00Z",
                "updatedAt": "2024-03-16T19:00:00Z"
            }
        ],
        "total": 2,
        "page": 1,
        "size": 20,
        "pages": 1
    }
    ```
    """  # noqa: E501
    try:
        skip = (page - 1) * size
        query = select(MealLogModel).where(MealLogModel.user_id == user.id)
        if log_date:
            query = query.where(func.date(MealLogModel.created_at) == log_date)

        if meal_type:
            query = query.where(MealLogModel.meal_type == meal_type)

        if is_final_meal is not None:
            query = query.where(MealLogModel.is_final_meal == is_final_meal)

        if sort_order == "asc":
            query = query.order_by(MealLogModel.id.asc())
        else:
            query = query.order_by(MealLogModel.id.desc())

        if num_logs is not None:
            query = query.limit(num_logs)

        total = session.scalar(select(func.count()).select_from(query.subquery())) or 0
        meal_logs = session.scalars(query.offset(skip).limit(size)).all()
        pages = ceil(total / size)

        return PaginatedResponse(
            items=[
                MealLogResponse(**meal_log_to_dict(meal_log)) for meal_log in meal_logs
            ],
            total=total,
            page=page,
            size=size,
            pages=pages,
        )
    except Exception as e:
        logger.exception(f"Error retrieving meal logs: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@meal_logs_router.get(
    "/meal_logs/{meal_log_id}",
    response_model=MealLogResponse,
    tags=["Meal Logs"],
    summary="Get a specific meal log",
)
async def get_meal_log(
    meal_log_id: int,
    user: Annotated[User, Depends(get_current_user)],
    session: Annotated[Session, Depends(get_db_session)],
):
    """
    Get a specific meal log.

    - **meal_log_id**: The ID of the meal log to retrieve

    Returns:
    - The requested meal log

    Example response:
    ```
    {
        "id": 1,
        "meal_type": "lunch",
        "description": "Grilled chicken salad with mixed greens, tomatoes, and balsamic vinaigrette",
        "analysis": {
            "score": 5,
            "summary": "Excellent meal choice. The addition of avocado provides healthy fats, enhancing nutrient absorption and promoting satiety.",
            "recommendations": "Consider adding a portion of whole grains for a balanced meal.",
        },
        "is_final_meal": false,
        "createdAt": "2024-03-16T12:30:00Z",
        "updatedAt": "2024-03-16T12:30:00Z"
    }
    """
    try:
        db_meal_log = crud_get_meal_log(session, user.id, meal_log_id)
        if db_meal_log is None:
            raise HTTPException(status_code=404, detail="Meal log not found")
        return MealLogResponse(**meal_log_to_dict(db_meal_log))
    except Exception as e:
        logger.exception(f"Error retrieving meal log: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@meal_logs_router.put(
    "/meal_logs/{meal_log_id}",
    response_model=MealLogResponse,
    tags=["Meal Logs"],
    summary="Update a meal log",
)
async def update_meal_log(
    meal_log_id: int,
    meal_log: MealLogRequest,
    user: Annotated[User, Depends(get_current_user)],
    session: Annotated[Session, Depends(get_db_session)],
):
    """
    Update a specific meal log.

    - **meal_log_id**: The ID of the meal log to update
    - **meal_log**: The updated meal log data

    Returns:
    - The updated meal log

    Example request:
    ```
    {
        "meal_type": "lunch",
        "description": "Grilled chicken salad with mixed greens, avocado, and \
olive oil dressing",
        "is_final_meal": false
    }
    ```

    Example response:
    ```
    {
        "id": 1,
        "meal_type": "lunch",
        "description": "Grilled chicken salad with mixed greens, tomatoes, and balsamic vinaigrette",
        "analysis": {
            "score": 5,
            "summary": "Excellent meal choice. The addition of avocado provides healthy fats, enhancing nutrient absorption and promoting satiety.",
            "recommendations": "Consider adding a portion of whole grains for a balanced meal.",
        },
        "is_final_meal": false,
        "createdAt": "2024-03-16T12:30:00Z",
        "updatedAt": "2024-03-16T13:15:00Z"
    }
    """
    try:
        db_meal_log = crud_get_meal_log(session, user.id, meal_log_id)
        if db_meal_log is None:
            raise HTTPException(status_code=404, detail="Meal log not found")
        updated_meal_log = crud_update_meal_log(
            session, db_meal_log, meal_log.model_dump()
        )
        # Invalidate nutrition-specific caches
        await invalidate_user_caches(user.id, invalidate_nutrition=True)

        return MealLogResponse(**meal_log_to_dict(updated_meal_log))
    except Exception as e:
        logger.exception(f"Error updating meal log: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@meal_logs_router.delete(
    "/meal_logs/{meal_log_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    tags=["Meal Logs"],
    summary="Delete a meal log",
)
async def delete_meal_log(
    meal_log_id: int,
    user: Annotated[User, Depends(get_current_user)],
    session: Annotated[Session, Depends(get_db_session)],
):
    """
    Delete a specific meal log.

    - **meal_log_id**: The ID of the meal log to delete

    Returns:
    - 204 No Content if the meal log was successfully deleted
    - 404 Not Found if the meal log doesn't exist


    Example response:
    ```
    No content (204 status code)
    ```
    """
    db_meal_log = crud_get_meal_log(session, user.id, meal_log_id)
    if db_meal_log is None:
        raise HTTPException(status_code=404, detail="Meal log not found")

    try:
        crud_delete_meal_log(session, db_meal_log)
        return None
    except Exception as e:
        logger.exception(f"Error deleting meal log: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")
