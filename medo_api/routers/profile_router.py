import logging
from datetime import date, datetime
from typing import Optional

from fastapi import APIRouter, Depends, HTTPException, status
from pydantic import BaseModel, EmailStr
from sqlalchemy.orm import Session

from ..ai.profile_generator import generate_user_profile
from ..ai.recommendations import invalidate_user_caches
from ..cache.redis_cache import RedisCache
from ..db.crud import update_user
from ..db.engine import get_db_session
from ..db.models import User
from .auth import get_current_user

profile_router = APIRouter()

logger = logging.getLogger(__name__)
redis_cache = RedisCache()


class UserProfile(BaseModel):
    name: Optional[str] = None
    email: Optional[EmailStr] = None
    dob: Optional[date] = None
    gender: Optional[str] = None
    profile_bio_data: Optional[str] = None
    user_attributes: Optional[list[dict[str, str]]] = None

    model_config = {
        "json_schema_extra": {
            "example": {
                "name": "<PERSON>",
                "email": "<EMAIL>",
                "dob": "1988-01-01",
                "gender": "Male",
                "profile_bio_data": """The user is a 36-year-old male who weighs 198 pounds
                    and is 79 inches tall. The user sleeps 7-8 hours per night and takes
                    naps during the day for about 1-2 hours. The user does not currently smoke
                    cigarettes, vape, or use tobacco products, though they had previously smoked
                    cigarettes for about a year before quitting 5 years ago.""",  # noqa: E501
                "user_attributes": [
                    {"question": "What is your gender?", "answer": "Male"},
                    {"question": "How old are you?", "answer": "36"},
                    {"question": "What is your weight in pounds?", "answer": "198"},
                    {"question": "What is your height in inches?", "answer": "79"},
                    {
                        "question": "How many hours do you sleep per night?",
                        "answer": "7-8 hours",
                        "extra": "I take naps during the day for about 1-2 hours",
                    },
                    {
                        "question": "Do you smoke cigarettes, vape or use tobacco products?",  # noqa: E501
                        "answer": "No",
                        "details": "I smoked cigarettes for about a year, but I quit 5 years ago.",  # noqa: E501
                    },
                ],
            }
        }
    }


class GenerateProfileBioRequest(BaseModel):
    use_existing_user_attributes: bool = False
    user_attributes: Optional[list[dict[str, str]]] = None


def retrieve_profile_data(user: User) -> UserProfile:
    """
    Retrieve the profile data for the user with only specified fields
    """
    user_attributes = user.profile.user_attributes if user.profile else None

    return UserProfile(
        name=user.name,
        email=user.email,
        dob=user.dob,
        gender=user.gender,
        profile_bio_data=(
            user.profile.profile_bio_data
            if user.profile and user.profile.profile_bio_data
            else None
        ),
        user_attributes=user_attributes,
    )


@profile_router.patch("/profile", tags=["User Profile"], response_model=UserProfile)
async def update_profile(
    profile_data: UserProfile,
    user: User = Depends(get_current_user),
    session: Session = Depends(get_db_session),
):
    """
    Update the profile for the authenticated user.

    This endpoint allows updating the user's profile information.

    Parameters:
        An object containing profile information. One or more
        fields can be included:
    - name: (string), the user's name
    - email: (string), the user's email
    - dob: (date), the user's date of birth in ISO 8601 date format (YYYY-MM-DD)
    - gender: (string), the user's gender
    - profile_bio_data: (string), the user's profile bio data
    - user_attributes: (list[dict[str, str]]), the user's personal attributes and
        lifestyle factors

    Returns:
        The updated profile information

    Example request body:
    ```
    {
        "name": "John Doe",
        "email": "<EMAIL>",
        "dob": "1988-01-01",
        "gender": "Male",
        "profile_bio_data": "The user is a 36-year-old male who weighs 198 pounds
            and is 79 inches tall. The user sleeps 7-8 hours per night and takes
            naps during the day for about 1-2 hours. The user does not currently smoke
            cigarettes, vape, or use tobacco products, though they had previously smoked
            cigarettes for about a year before quitting 5 years ago.",
        "user_attributes": [
            {"question": "What is your gender?", "answer": "Male"},
            {"question": "How old are you?", "answer": "36"},
            {"question": "What is your weight in pounds?", "answer": "198"},
            {"question": "What is your height in inches?", "answer": "79"},
            {
                "question": "How many hours do you sleep per night?",
                "answer": "7-8 hours",
                "extra": "I take naps during the day for about 1-2 hours"
            },
            {
                "question": "Do you smoke cigarettes, vape or use tobacco products?",
                "answer": "No",
                "details": "I smoked cigarettes for about a year, but I quit 5 years ago."
            }
        ]
    }
    ```

    Example response:
    ```
    {
        "name": "John Doe",
        "email": "<EMAIL>",
        "dob": "1988-01-01",
        "gender": "Male",
        "profile_bio_data": "John Doe is a 36-year-old male who weighs 198 pounds,
                is 79 inches tall, does not smoke or use tobacco products, and engages in
                moderate to vigorous physical activity 6-7 days a week. The user sleeps for
                7-8 hours per night.",
        "user_attributes": [
            {"question": "What is your gender?", "answer": "Male"},
            {"question": "How old are you?", "answer": "36"},
            {"question": "What is your weight in pounds?", "answer": "198"},
            {"question": "What is your height in inches?", "answer": "79"},
            {
                "question": "How many hours do you sleep per night?",
                "answer": "7-8 hours",
                "extra": "I take naps during the day for about 1-2 hours"
            },
            {
                "question": "Do you smoke cigarettes, vape or use tobacco products?",
                "answer": "No",
                "details": "I smoked cigarettes for about a year, but I quit 5 years ago."
            }
        ]
    }
    ```
    """  # noqa: E501

    try:
        # Only update the fields that have values
        updated_data = profile_data.model_dump(exclude_unset=True)

        # Check if user_attributes have changed
        if "user_attributes" in updated_data:
            # Generate new profile bio based on updated attributes
            new_bio = await generate_user_profile(
                user_attributes=updated_data["user_attributes"], user=user
            )
            updated_data["profile_bio_data"] = new_bio

        updated_user = update_user(session, user, updated_data)

        # Force cache invalidation by setting a new version timestamp
        current_timestamp = datetime.now().timestamp()

        # Invalidate all caches first
        await invalidate_user_caches(user.id)

        # Set new profile version with a unique timestamp
        profile_version_key = f"profile_version:{user.id}"
        redis_cache.set(profile_version_key, str(current_timestamp), expire=3600)

        logger.info(
            f"Updated profile version for user {user.id} to {current_timestamp}"
        )

        return retrieve_profile_data(updated_user)
    except Exception as e:
        logger.error(f"Error updating profile: {str(e)}")
        raise HTTPException(status_code=500, detail="Error updating profile")


@profile_router.get("/profile", tags=["User Profile"], response_model=UserProfile)
async def get_profile(
    user: User = Depends(get_current_user),
):
    """
    Retrieve the profile for the authenticated user.

    This endpoint returns the user's profile information.

    Returns:
        The user's profile information

    Example response:
    ```
    {
        "name": "John Doe",
        "email": null,
        "dob": "1988-01-01",
        "gender": "Male",
        "profile_bio_data": "The user is a 36-year-old male who weighs 198 pounds
            and is 79 inches tall. The user sleeps 7-8 hours per night and takes
            naps during the day for about 1-2 hours. The user does not currently smoke
            cigarettes, vape, or use tobacco products, though they had previously smoked
            cigarettes for about a year before quitting 5 years ago.",
        "user_attributes": [
            {"question": "What is your gender?", "answer": "Male"},
            {"question": "How old are you?", "answer": "36"},
            {"question": "What is your weight in pounds?", "answer": "198"},
            {"question": "What is your height in inches?", "answer": "79"},
            {
                "question": "How many hours do you sleep per night?",
                "answer": "7-8 hours",
                "extra": "I take naps during the day for about 1-2 hours"
            },
            {
                "question": "Do you smoke cigarettes, vape or use tobacco products?",
                "answer": "No",
                "details": "I smoked cigarettes for about a year, but I quit 5 years ago."
            }
        ]
    }
    ```
    """  # noqa: E501

    return retrieve_profile_data(user)


@profile_router.post("/profile/bio", tags=["User Profile"], response_model=str)
async def generate_profile_bio(
    data: GenerateProfileBioRequest,
    user: User = Depends(get_current_user),
    session: Session = Depends(get_db_session),
):
    """
    Generate a profile bio based on the provided user attributes.

    This endpoint uses AI to generate a concise profile bio from
    the given user attributes.

    Parameters:
        GenerateProfileBioRequest: An object containing use_existing_user_attributes
        and user_attributes

    Returns:
        str: The generated profile bio

    Errors:
        422 UNPROCESSABLE ENTITY:
            - user_attributes are required if use_existing_user_attributes is false
            - user_attributes are not required if use_existing_user_attributes is true

    Example request body:
    ```
    {
        "use_existing_user_attributes": false,
        "user_attributes": [
            {"question": "What is your gender?", "answer": "Male"},
            {"question": "How old are you?", "answer": "36"},
            {"question": "What is your weight in pounds?", "answer": "198"},
            {"question": "What is your height in inches?", "answer": "79"},
            {"question": "How many hours do you sleep per night?", "answer": "7-8 hours"},
            {
                "question": "Do you smoke cigarettes, vape or use tobacco products?",
                "answer": "No",
                "details": "I smoked cigarettes for about a year, but I quit 5 years ago."
            }
        ]
    }
    ```

    Example response:
    ```
    The user is a 36-year-old male who weighs 198 pounds and is 79 inches tall.
    The user sleeps 7-8 hours per night and takes naps during the day for about 1-2 hours.
    The user does not currently smoke cigarettes, vape, or use tobacco products,
    though they had previously smoked cigarettes for about a year before quitting 5 years ago.
    ```
    """  # noqa: E501
    if data.use_existing_user_attributes is False and data.user_attributes is None:
        raise HTTPException(
            status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail="User attributes are required",
        )
    elif data.use_existing_user_attributes is True and data.user_attributes is not None:
        raise HTTPException(
            status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail="User attributes are not required",
        )
    elif data.use_existing_user_attributes is True:
        user_attributes = user.profile.user_attributes if user.profile else []
    else:
        user_attributes = data.user_attributes
    profile_bio = await generate_user_profile(user_attributes, user)
    update_user(session, user, {"profile_bio_data": profile_bio})
    return profile_bio
