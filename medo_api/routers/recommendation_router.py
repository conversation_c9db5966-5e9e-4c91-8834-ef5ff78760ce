import asyncio
import gc
import logging
from typing import Union

from fastapi import APIRouter, Depends, <PERSON>er, HTTPException, Query
from pydantic import BaseModel, ConfigDict, Field
from sqlalchemy.orm import Session

from ..ai.recommendations import PillarRecommendation, generate_pillar_recommendation
from ..db.engine import get_db_session
from ..db.models import Pillar, User
from .auth import get_current_user

logger = logging.getLogger(__name__)
recommendation_router = APIRouter()


class OverallRecommendations(BaseModel):
    exercise: PillarRecommendation = Field(
        ..., description="Exercise pillar recommendation"
    )
    sleep: PillarRecommendation = Field(..., description="Sleep pillar recommendation")
    nutrition: PillarRecommendation = Field(
        ..., description="Nutrition pillar recommendation"
    )
    socialConnection: PillarRecommendation = Field(
        ..., description="Social connection pillar recommendation"
    )
    emotionalWellbeing: PillarRecommendation = Field(
        ..., description="Emotional wellbeing pillar recommendation"
    )

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "exercise": {
                    "strengths": "Regular gym attendance, consistent cardio routine",
                    "areasForImprovement": "Incorporate more strength training, \
increase workout intensity",
                },
                "sleep": {
                    "strengths": "Consistent sleep schedule, good sleep environment",
                    "areasForImprovement": "Reduce screen time before bed, aim for \
7-8 hours of sleep",
                },
                "nutrition": {
                    "strengths": "Regular meal times, good variety of vegetables",
                    "areasForImprovement": "Increase protein intake, reduce processed \
food consumption",
                },
                "socialConnection": {
                    "strengths": "Regular family time, active in community groups",
                    "areasForImprovement": "Reach out to old friends, join a new \
social club",
                },
                "emotionalWellbeing": {
                    "strengths": "Practice daily gratitude, good stress management \
techniques",
                    "areasForImprovement": "Incorporate meditation, seek professional \
support if needed",
                },
            }
        }
    )


@recommendation_router.get(
    "/recommendations",
    response_model=Union[OverallRecommendations, PillarRecommendation],
    tags=["Recommendations"],
    summary="Get recommendations for all pillars or a specific pillar",
)
async def get_recommendations(
    user: User = Depends(get_current_user),
    session: Session = Depends(get_db_session),
    pillar: str | None = Query(
        None, description="Optional pillar to filter recommendations"
    ),
):
    """
    Get overall recommendations for all pillars or a specific pillar

    This endpoint generates recommendations for each pillar (exercise, sleep, nutrition,
    social connection, and emotional wellbeing) based on the user's data and history.

    Parameters:
    - **user**: The authenticated user (automatically injected)
    - **session**: The database session (automatically injected)
    - **pillar**: Optional. The pillar to generate recommendations for

    Returns:
    - An OverallRecommendations object containing recommendations for each pillar

    Example response if no pillar is specified:
    ```
    {
      "exercise": {
        "strengths": "Regular gym attendance, consistent cardio routine",
        "areasForImprovement": "Incorporate more strength training, increase workout \
intensity"
      },
      "sleep": {
        "strengths": "Consistent sleep schedule, good sleep environment",
        "areasForImprovement": "Reduce screen time before bed, aim for 7-8 hours of \
sleep"
      },
      "nutrition": {
        "strengths": "Regular meal times, good variety of vegetables",
        "areasForImprovement": "Increase protein intake, reduce processed food \
consumption"
      },
      "socialConnection": {
        "strengths": "Regular family time, active in community groups",
        "areasForImprovement": "Reach out to old friends, join a new social club"
      },
      "emotionalWellbeing": {
        "strengths": "Practice daily gratitude, good stress management techniques",
        "areasForImprovement": "Incorporate meditation, seek professional support if \
needed"
      }
    }
    Example response if a specific pillar is specified:
    ```
    {
      "strengths": "Regular gym attendance, consistent cardio routine",
      "areasForImprovement": "Incorporate more strength training, increase workout \
intensity"
    }
    ```
    """
    if pillar:
        try:
            pillar_enum = Pillar(pillar)
            recommendation = await generate_pillar_recommendation(
                session=session,
                user=user,
                pillar=pillar_enum,
            )
            return recommendation
        except ValueError as exc:
            raise HTTPException(
                status_code=400,
                detail=f"Invalid pillar value. Must be one of: {[p.value for p in Pillar]}",
            ) from exc
    recommendations = []
    for pillar_enum in Pillar:
        recommendation = await generate_pillar_recommendation(
            session=session,
            user=user,
            pillar=pillar_enum,
        )
        recommendations.append(recommendation)
        gc.collect()

    return OverallRecommendations(
        **dict(zip([pillar.value for pillar in Pillar], recommendations))
    )
