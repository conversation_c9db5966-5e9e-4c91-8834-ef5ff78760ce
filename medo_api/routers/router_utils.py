from datetime import datetime, timedelta
from typing import Optional

from sqlalchemy import select
from sqlalchemy.orm import Session

from ..db.models import Meal<PERSON>og


def get_recent_meal_logs(
    session: Session, user_id: int, days: int = 14, utc_offset: Optional[float] = None
) -> str:
    start_date = datetime.utcnow() - timedelta(days=days)

    if utc_offset is not None:
        start_date += timedelta(hours=utc_offset)

    meal_logs = session.scalars(
        select(MealLog)
        .where(MealLog.user_id == user_id)
        .where(MealLog.created_at >= start_date)
        .order_by(MealLog.created_at.desc())
    ).all()

    formatted_logs = "\n".join(
        f"{(log.created_at + timedelta(hours=utc_offset or 0)).strftime('%Y-%m-%d %H:%M:%S')}: {log.meal_type} - {log.description}"  # noqa: E501
        for log in meal_logs
    )

    return (
        formatted_logs
        if formatted_logs
        else "No meal logs available for the specified period."
    )
