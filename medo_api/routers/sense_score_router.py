import logging
import os
from datetime import datetime
from typing import Optional

import requests
from fastapi import APIRouter, Depends, HTTPException, Query, status
from pydantic import BaseModel, Field, ValidationError
from sqlalchemy.exc import IntegrityError
from sqlalchemy.orm import Session

from ..ai.profile_generator import generate_user_profile
from ..db.crud import create_checkin, get_checkins
from ..db.engine import get_db_session
from ..db.models import InitialPillars, User, UserProfile
from ..schemas.sense_score import (
    CheckinReponse,
    CheckinRequest,
    CheckinsResponse,
    PillarsResponse,
    PillarVariables,
    SenseScoreResponse,
    TypeformCaptureRequest,
)
from ..schemas.typeform import Answer, FormStructure, TypeformResponseItem
from .auth import get_current_user

logger = logging.getLogger(__name__)
sense_score_router = APIRouter()


def json_serial(obj):
    """JSON serializer for objects not serializable by default json code"""
    if isinstance(obj, datetime):
        return obj.isoformat()
    raise TypeError(f"Type {type(obj)} not serializable")


def datetime_to_isoformat(obj):
    if isinstance(obj, datetime):
        return obj.isoformat()
    return obj


# ---------- Models ----------


class CaptureRequestData(BaseModel):
    formId: str
    responseId: str


class CheckinsResponse(BaseModel):
    checkins: list[CheckinReponse]
    latestCheckinTimestamp: Optional[str] = Field(None, alias="latestCheckinTimestamp")


class PillarVariables(BaseModel):
    sleep: int
    exercise: int
    nutrition: int
    emotional: int
    social: int
    score: int
    additional_data: dict[str, dict[str, str]] = {}
    user_attributes: list[dict[str, str]] = []


# ---------- Routes ----------


@sense_score_router.post(
    "/typeform_survey",
    response_model=SenseScoreResponse,
    tags=["SENSE Score"],
    summary="Capture completed Typeform survey",
)
async def capture_completed_survey(
    data: TypeformCaptureRequest,
    user: User = Depends(get_current_user),
    session: Session = Depends(get_db_session),
):
    """
    Capture and process a completed Typeform survey.

    This endpoint receives the form ID and response ID of a completed Typeform survey,
    fetches the response data, and creates initial pillars for the user based on the
    survey results.

    Parameters:
        TypeformCaptureRequest: Object containing formId and responseId

    Returns:
        SenseScoreResponse: A JSON object containing pillars and senseScore

    Raises:
        404 Not Found: If no Typeform response is found for the given IDs

    Raises:
        400 Bad Request: If the user has already completed a survey

    Raises:
        403 Forbidden: If the Typeform API key is invalid or with limited permissions

    Example request:
    ```
    {
        "formId": "abc123",
        "responseId": "def456"
    }
    ```

    Example response:
    ```
    {
        "pillars": {
            "sleep": 45,
            "exercise": 85,
            "nutrition": 85,
            "socialConnection": 80,
            "emotionalWellbeing": 70,
        },
        "senseScore": 73,
    }
    ```
    """
    if user.initial_pillars is not None:
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST, "User has already completed a survey"
        )

    typeform_response = fetch_typeform_responses(
        form_id=data.formId, response_id=data.responseId
    )
    if not typeform_response:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Typeform response not found or incomplete",
        )

    form_structure = fetch_typeform_structure(data.formId)
    if not form_structure:
        raise HTTPException(status.HTTP_404_NOT_FOUND, "No typeform structure found")

    required_fields = ["sleep", "exercise", "nutrition", "emotional", "social", "score"]
    variables = get_pillar_and_score_variables(
        typeform_response, required_fields, form_structure
    )

    # Convert datetime objects to ISO format strings in the typeform_response
    typeform_result = {
        k: datetime_to_isoformat(v) for k, v in typeform_response.model_dump().items()
    }

    # Save the survey result
    initial_pillars = InitialPillars(
        user=user,
        typeform_result=typeform_result,
        sleep=variables.sleep,
        exercise=variables.exercise,
        nutrition=variables.nutrition,
        emotional_wellbeing=variables.emotional,
        social_connection=variables.social,
        sense_score=variables.score,
        additional_data=variables.additional_data,
    )
    # Generate profile bio data
    profile_bio_data = await generate_user_profile(variables.user_attributes, user)

    # Check if user profile exists, if so update it, otherwise create new
    existing_profile = (
        session.query(UserProfile).filter(UserProfile.user_id == user.id).first()
    )
    if existing_profile:
        existing_profile.user_attributes = variables.user_attributes
        existing_profile.profile_bio_data = profile_bio_data
    else:
        user_profile = UserProfile(
            user=user,
            user_attributes=variables.user_attributes,
            profile_bio_data=profile_bio_data,
        )
        session.add(user_profile)

    try:
        session.add(initial_pillars)
        session.commit()
    except IntegrityError:
        session.rollback()
        raise HTTPException(
            status_code=400, detail="User has already completed the initial survey."
        )

    return {
        "pillars": initial_pillars.to_json(),
        "senseScore": initial_pillars.sense_score,
        "profileBioData": profile_bio_data,
    }


@sense_score_router.get(
    "/initial_pillars",
    response_model=PillarsResponse,
    tags=["SENSE Score"],
    summary="Get initial pillars",
)
async def get_initial_pillars_api(
    user: User = Depends(get_current_user), session: Session = Depends(get_db_session)
):
    """
    Retrieve the initial pillars for the authenticated user.

    This endpoint returns the user's initial pillar values, which are used to calculate
    the SENSE score. These values are typically set after completing the initial survey.

    Returns:
        PillarsResponse: A JSON object containing the initial pillar values

    Raises:
        404 Not Found: If the user's initial pillars have not been set


    Example response:
    ```
    {
        "sleep": 75,
        "exercise": 80,
        "nutrition": 70,
        "emotional": 78,
        "social": 85,
        "score": 77,
    }
    ```
    """
    if user.initial_pillars is None:
        raise HTTPException(404)
    else:
        return user.initial_pillars.to_json()


@sense_score_router.delete(
    "/initial_pillars",
    tags=["SENSE Score"],
    summary="Delete initial pillars (Testing Only)",
)
async def delete_initial_pillars(
    user: User = Depends(get_current_user),
    session: Session = Depends(get_db_session),
):
    """
    Delete the initial pillars for the authenticated user.

    This endpoint is for testing purposes only, to allow testing the typeform survey
    workflow.

    Raises:
        404 Not Found: If the user's initial pillars are not set

    Example response:
    ```
    "deleted"
    ```
    """
    if user.initial_pillars is None:
        raise HTTPException(404)

    session.delete(user.initial_pillars)
    session.commit()
    return "deleted"


@sense_score_router.get(
    "/sense_score",
    response_model=SenseScoreResponse,
    tags=["SENSE Score"],
    summary="Get current SENSE score",
)
async def get_sense_score_api(
    user: User = Depends(get_current_user),
    session: Session = Depends(get_db_session),
):
    """
    Retrieve the current SENSE score for the authenticated user.

    This endpoint calculates and returns the user's current SENSE score and current
    pillar values.

    Returns:
        SenseScoreResponse: An object containing SENSE score details and pillars

    Raises:
        404 Not Found: If there is not SENSE score for the user (e.g. Initial pillars have not been set)

    Example response:
    ```
    {
        "senseScore": 77,
        "pillars": {
            "sleep": 75,
            "exercise": 80,
            "nutrition": 70,
            "socialConnection": 85,
            "emotionalWellbeing": 78,
        }
    }
    ```
    """  # noqa: E501
    if user.initial_pillars is None:
        raise HTTPException(404, "Initial pillars not found")

    pillars = user.initial_pillars

    return SenseScoreResponse(
        senseScore=pillars.sense_score,
        pillars=PillarsResponse(
            sleep=pillars.sleep,
            exercise=pillars.exercise,
            nutrition=pillars.nutrition,
            socialConnection=pillars.social_connection,
            emotionalWellbeing=pillars.emotional_wellbeing,
        ),
    )


@sense_score_router.get(
    "/checkins",
    response_model=CheckinsResponse,
    tags=["Checkins"],
    summary="Get user checkins",
)
def get_checkins_api(
    from_datetime: Optional[datetime] = Query(
        None,
        alias="from",
        description="""ISO8601 formatted string. Checkins created after this time will
        be returned.""",
    ),
    user: User = Depends(get_current_user),
    session=Depends(get_db_session),
):
    """
    Retrieve checkins for the authenticated user.

    This endpoint returns a list of checkins for the user, optionally filtered by date.
    It also provides the timestamp of the latest checkin, if any.

    Query Parameters:
        from_datetime (optional): ISO8601 formatted string. Checkins created after
                                  this time will be returned.

    Returns:
        CheckinsResponse: An object containing a list of checkins and the timestamp
                          of the latest checkin

    ```
    Example response:
        {
            "checkins": [
                {
                    "nutrition": "Balanced meal with vegetables and lean protein",
                    "emotionalWellbeing": 4,
                    "socialConnection": 3,
                    "createdAt": "2023-07-01T08:11:00Z"
                }
            ],
            "latestCheckinTimestamp": "2023-07-01T08:11:00Z"
        }
    ```
    """
    checkins = get_checkins(session=session, user=user, from_datetime=from_datetime)
    checkin_responses = [checkin.to_json() for checkin in checkins]

    latest_checkin_timestamp = None
    if checkin_responses:
        latest_checkin_timestamp = max(
            datetime.fromisoformat(checkin.createdAt) for checkin in checkin_responses
        ).isoformat()

    return CheckinsResponse(
        checkins=checkin_responses, latestCheckinTimestamp=latest_checkin_timestamp
    )


@sense_score_router.post(
    "/checkins",
    response_model=CheckinReponse,
    tags=["Checkins"],
    summary="Create a new checkin",
)
def post_checkins_api(
    data: CheckinRequest,
    user: User = Depends(get_current_user),
    session: Session = Depends(get_db_session),
):
    """
    Create a periodic checkin for a user.

    This endpoint allows users to submit a checkin with information about their
    nutrition, emotional wellbeing, and social connection. At least one field
    must be provided.

    Parameters:
        CheckinRequest: The checkin data

    Returns:
        CheckinReponseJSON: The created checkin data

    Raises:
        422 Unprocessable Entity: If no fields are provided


    Example request body:
    ```
    {
        "nutrition": "Balanced meal with vegetables and lean protein",
        "emotionalWellbeing": 4,
        "socialConnection": 3
    }
    ```

    Example response:
    ```
    {
        "nutrition": "Balanced meal with vegetables and lean protein",
        "emotionalWellbeing": 4,
        "socialConnection": 3,
        "createdAt": "2023-08-19T14:30:00.000Z"
    }
    ```
    """
    if (
        data.nutrition is None
        and data.emotionalWellbeing is None
        and data.socialConnection is None
    ):
        raise HTTPException(
            422,
            (
                "At least one of 'nutrition', "
                "'emotionalWellbeing' or 'socialConnection'"
                " must be specified"
            ),
        )

    checkin = create_checkin(
        session=session,
        user=user,
        nutrition=data.nutrition,
        emotional_wellbeing=data.emotionalWellbeing,
        social_connection=data.socialConnection,
    )
    return checkin.to_json()


# ---------- Algorithms ----------


def categorize_fields_to_pillars(
    form_structure: FormStructure,
) -> dict[str, str]:
    """Create a mapping of field refs to pillar categories"""
    field_to_pillar = {}
    for logic_item in form_structure["logic"]:
        if logic_item["type"] == "field":
            ref = logic_item["ref"]
            for action in logic_item["actions"]:
                if action["action"] == "add":
                    target = action["details"]["target"]["value"]
                    if target in [
                        "sleep",
                        "exercise",
                        "nutrition",
                        "social",
                        "emotional",
                    ]:
                        field_to_pillar[ref] = target
                        break
            if ref not in field_to_pillar:
                field_to_pillar[ref] = "other"

    return field_to_pillar


def get_pillar_and_score_variables(
    typeform_response: TypeformResponseItem,
    required_fields: list[str],
    form_structure: FormStructure,
) -> PillarVariables:
    variables: dict[str, int] = {}
    user_attributes: list[dict[str, str]] = []
    additional_data: dict[str, dict[str, str]] = {
        "other": {},
    }

    # Create a mapping of field IDs to questions
    questions = {field["id"]: field["title"] for field in form_structure["fields"]}

    field_to_pillar = categorize_fields_to_pillars(form_structure)

    for variable in typeform_response.variables:
        if variable.key in required_fields:
            if variable.number is None:
                raise HTTPException(
                    status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                    detail=f"Missing value for required field: {variable.key}",
                )

            # Round float values to integers
            if isinstance(variable.number, float):
                variables[variable.key] = round(variable.number)
            else:
                variables[variable.key] = variable.number
            additional_data[variable.key] = {}

    for field in required_fields:
        if field not in variables or variables[field] is None:
            raise HTTPException(
                status.HTTP_404_NOT_FOUND, f"Missing '{field}' variable"
            )

    for answer in typeform_response.answers:
        ref = answer.field.ref
        pillar = field_to_pillar.get(ref, "other")
        answer_text = get_answer_text(answer)

        question_id = answer.field.id
        question_text = questions.get(question_id, "Unknown question")
        user_attributes.append({"question": question_text, "answer": answer_text})
        additional_data[pillar][question_text] = answer_text

    # Rename the keys social to social_connection and emotional to emotional_wellbeing
    additional_data["social_connection"] = additional_data["social"]
    additional_data["emotional_wellbeing"] = additional_data["emotional"]
    del additional_data["social"]
    del additional_data["emotional"]

    return PillarVariables(
        **variables, additional_data=additional_data, user_attributes=user_attributes
    )


def get_answer_text(answer: Answer) -> str:
    if answer.type == "choice":
        answer_text = answer.choice.label
    elif answer.type == "choices":
        answer_text = ", ".join(answer.choices.labels)
    elif answer.type == "number":
        answer_text = str(answer.number)
    elif answer.type == "text":
        answer_text = answer.text
    else:
        answer_text = "Unsupported answer type"

    return answer_text


def fetch_typeform_responses(
    form_id: str, response_id: str
) -> TypeformResponseItem | None:
    try:
        responses = get_typeform_responses(form_id=form_id)

        if "items" not in responses:
            logger.error("Invalid typeform API response - no 'items' field")
            return None

        items = responses["items"]
        logger.info(f"{len(items)} items received")

        for item in items:
            try:
                response_item = TypeformResponseItem(**item)
                if response_item.response_id == response_id:
                    if response_item.response_type != "completed":
                        raise HTTPException(
                            status_code=status.HTTP_400_BAD_REQUEST,
                            detail="User has not completed the survey",
                        )
                    return response_item
            except ValidationError as e:
                logger.exception(f"Invalid typeform response item: {e}")
        logger.warning(f"Response ID {response_id} not found in typeform responses")
        return None

    except requests.HTTPError as e:
        logger.error(f"HTTP error occurred: {e}")
        return None
    except KeyError as e:
        logger.error(f"Missing expected key: {e}")
        return None


def get_typeform_responses(form_id: str) -> dict:
    """Get the responses for a typeform"""
    typeform_api_key = os.environ["TYPEFORM_API_KEY"]
    base_url = "https://api.typeform.com"
    endpoint = f"/forms/{form_id}/responses"
    headers = {"Authorization": f"Bearer {typeform_api_key}"}

    try:
        response = requests.get(f"{base_url}{endpoint}", headers=headers)
        response.raise_for_status()
        return response.json()
    except requests.exceptions.HTTPError as e:
        if e.response.status_code == 403:
            logger.error(f"403 Forbidden error when fetching Typeform responses: {e}")
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Unable to access Typeform API. Please check your access token and permissions.",  # noqa: E501
            ) from e
        else:
            logger.error(f"HTTP error occurred when fetching Typeform responses: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="An error occurred while processing your request.",
            ) from e
    except Exception as e:
        logger.error(f"Unexpected error when fetching Typeform responses: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An unexpected error occurred while processing your request.",
        ) from e


def fetch_typeform_structure(form_id: str) -> dict:
    typeform_api_key = os.environ["TYPEFORM_API_KEY"]
    base_url = "https://api.typeform.com"
    endpoint = f"/forms/{form_id}"
    headers = {"Authorization": f"Bearer {typeform_api_key}"}

    try:
        response = requests.get(f"{base_url}{endpoint}", headers=headers)
        response.raise_for_status()
        return response.json()
    except requests.exceptions.HTTPError as e:
        if e.response.status_code == 403:
            logger.error(f"403 Forbidden error when fetching Typeform structure: {e}")
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Unable to access Typeform API. Please check your access token and permissions.",  # noqa: E501
            ) from e
        else:
            logger.error(f"HTTP error occurred when fetching Typeform structure: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="An error occurred while processing your request.",
            ) from e
    except Exception as e:
        logger.error(f"Unexpected error when fetching Typeform structure: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An unexpected error occurred while processing your request.",
        ) from e


def calculate_sense_score(initial_pillars: InitialPillars) -> int:
    sum_of_weighed_scores = (
        ((initial_pillars.sleep / 20) * 0.20)
        + ((initial_pillars.exercise / 50) * 0.20)
        + ((initial_pillars.nutrition / 45) * 0.25)
        + ((initial_pillars.social_connection / 35) * 0.15)
        + ((initial_pillars.emotional_wellbeing / 25) * 0.20)
    )
    return int(sum_of_weighed_scores * 100)
