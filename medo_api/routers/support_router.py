from fastapi import APIRouter
from toml import load as load_toml
from pathlib import Path
from pydantic import BaseModel, Field
from ..cache.redis_cache import RedisCache


# Config
with open(Path(__file__).resolve().parent / ".." / ".." / "pyproject.toml", "r") as f:
    project_config = load_toml(f)
    __version__ = project_config["tool"]["poetry"]["version"]


# Router
support_router = APIRouter()


# Models
class VersionResponse(BaseModel):
    """
    Represents the response for the API version endpoint.

    Attributes:
        version (str): The current version of the API.
    """

    version: str = Field(..., description="The current version of the API")

    model_config = {"json_schema_extra": {"example": {"version": "1.0.0"}}}


# Routes
@support_router.get(
    "/", response_model=VersionResponse, tags=["Support"], summary="Get API version"
)
async def version():
    """
    Retrieve the current version of the API.

    Returns:
        VersionResponse: An object containing the version number

    Example:
    ```
    {
        "version": "1.0.0"
    }
    ```
    """
    return {"version": __version__}

@support_router.get("/cache-stats")
async def get_cache_stats():
    redis_cache = RedisCache()
    stats = redis_cache.get_stats()
    total_hits = sum(s["hits"] for s in stats.values())
    total_misses = sum(s["misses"] for s in stats.values())
    total_requests = total_hits + total_misses
    
    return {
        "cache_stats": stats,
        "summary": {
            "total_keys": len(stats),
            "total_hits": total_hits,
            "total_misses": total_misses,
            "overall_hit_rate": (total_hits / total_requests) if total_requests > 0 else 0,
            "total_requests": total_requests
        }
    }
