# medo_api/schemas/meal_logs.py

from datetime import datetime
from typing import Any, Dict

from pydantic import BaseModel, Field


class MealLog(BaseModel):
    meal_type: str = Field(
        ..., description="Type of the meal (e.g., breakfast, lunch, dinner)"
    )
    description: str = Field(..., description="Description of the meal")
    is_final_meal: bool = Field(
        False, description="Indicates if this is the final meal of the day"
    )

    model_config = {
        "json_schema_extra": {
            "example": {
                "meal_type": "lunch",
                "description": "Grilled chicken salad with mixed greens and balsamic vinaigrette",  # noqa: E501
                "is_final_meal": False,
            }
        }
    }


class MealLogResponse(BaseModel):
    id: int = Field(..., description="Unique identifier for the meal log")
    meal_type: str = Field(..., description="Type of the meal")
    description: str = Field(..., description="Description of the meal")
    analysis: Dict[str, Any] = Field(
        ..., description="AI-generated analysis of the meal"
    )
    is_final_meal: bool = Field(
        ..., description="Indicates if this is the final meal of the day"
    )
    createdAt: datetime = Field(
        ..., description="Timestamp of when the meal log was created"
    )
    updatedAt: datetime = Field(
        ..., description="Timestamp of when the meal log was last updated"
    )

    model_config = {
        "json_schema_extra": {
            "example": {
                "id": 1,
                "meal_type": "lunch",
                "description": "Grilled chicken salad with mixed greens and balsamic vinaigrette",  # noqa: E501
                "analysis": {
                    "score": 5,
                    "summary": "Excellent meal choice. The addition of avocado provides healthy fats, enhancing nutrient absorption and promoting satiety.",  # noqa: E501
                    "recommendations": "Consider adding a portion of whole grains for a balanced meal.",  # noqa: E501
                },
                "is_final_meal": False,
                "createdAt": "2024-03-15T12:30:00Z",
                "updatedAt": "2024-03-15T12:30:00Z",
            }
        }
    }
