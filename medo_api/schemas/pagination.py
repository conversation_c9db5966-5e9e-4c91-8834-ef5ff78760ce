# medo_api/schemas/pagination.py

from pydantic import BaseModel, Field
from typing import Generic, TypeVar, List

T = TypeVar("T")


class PaginatedResponse(BaseModel, Generic[T]):
    items: List[T] = Field(..., description="List of items in the current page")
    total: int = Field(..., description="Total number of items across all pages")
    page: int = Field(..., description="Current page number")
    size: int = Field(..., description="Number of items per page")
    pages: int = Field(..., description="Total number of pages")

    model_config = {
        "json_schema_extra": {
            "example": {
                "items": [
                    {
                        "id": 1,
                        "meal_type": "lunch",
                        "description": """Grilled chicken salad with mixed greens and
                        balsamic vinaigrette""",
                        "analysis": """This meal is a balanced option with lean protein
                        from the grilled chicken and nutrients from the mixed greens.
                        The balsamic vinaigrette adds flavor without excessive calories.
                        Consider adding some whole grains or fruit to increase the
                        meal's fiber content.""",
                        "is_final_meal": False,
                        "createdAt": "2024-03-15T12:30:00Z",
                        "updatedAt": "2024-03-15T12:30:00Z",
                    }
                ],
                "total": 50,
                "page": 1,
                "size": 20,
                "pages": 3,
            }
        }
    }
