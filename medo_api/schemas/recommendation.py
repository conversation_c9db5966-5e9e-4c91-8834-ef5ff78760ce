from pydantic import BaseModel, Field
from datetime import datetime


class RecommendationResponse(BaseModel):
    id: int = Field(..., description="Unique identifier for the recommendation")
    category: str = Field(
        ...,
        description="""Category of the recommendation (e.g., 'exercise', 'sleep',
        'nutrition')""",
    )
    content: str = Field(..., description="The actual recommendation text")
    read: bool = Field(
        ...,
        description="Indicates whether the recommendation has been read by the user",
    )
    createdAt: datetime = Field(
        ..., description="Timestamp of when the recommendation was created"
    )

    model_config = {
        "json_schema_extra": {
            "example": {
                "id": 1,
                "category": "exercise",
                "content": """Try incorporating 30 minutes of moderate-intensity cardio
                exercise, such as brisk walking or cycling, into your daily routine to
                improve your cardiovascular health.""",
                "read": False,
                "createdAt": "2024-03-15T10:30:00Z",
            }
        }
    }
