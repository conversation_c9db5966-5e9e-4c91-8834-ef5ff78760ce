from pydantic import BaseModel, Field
from typing import Optional, List
from datetime import datetime


class ChatHistoryEntry(BaseModel):
    createdAt: datetime = Field(
        ..., description="Timestamp of when the chat entry was created"
    )
    question: str = Field(..., description="The question asked by the user")
    answer: str = Field(..., description="The answer provided by the system")

    model_config = {
        "json_schema_extra": {
            "example": {
                "createdAt": "2024-06-13T16:22:37.892377",
                "question": "What are the benefits of a Mediterranean diet?",
                "answer": "The Mediterranean diet has numerous benefits...",
            }
        }
    }


class CheckinRequest(BaseModel):
    nutrition: Optional[str] = Field(
        None, description="Description of the user's daily nutrition"
    )
    emotionalWellbeing: Optional[int] = Field(
        None, description="User's emotional state on a scale of 1-5"
    )
    socialConnection: Optional[int] = Field(
        None, description="User's social interactions on a scale of 1-5"
    )

    model_config = {
        "json_schema_extra": {
            "example": {
                "nutrition": """I ate a balanced meal with grilled chicken, quinoa, and
                mixed vegetables""",
                "emotionalWellbeing": 4,
                "socialConnection": 3,
            }
        }
    }


class CheckinReponse(CheckinRequest):
    createdAt: str = Field(..., description="Timestamp of when the checkin was created")

    model_config = {
        "json_schema_extra": {
            "example": {
                "nutrition": """I ate a balanced meal with grilled chicken, quinoa, and
                mixed vegetables""",
                "emotionalWellbeing": 4,
                "socialConnection": 3,
                "createdAt": "2024-06-13T16:22:37.892377",
            }
        }
    }


class PillarsResponse(BaseModel):
    sleep: int = Field(..., description="Sleep pillar score")
    exercise: int = Field(..., description="Exercise pillar score")
    nutrition: int = Field(..., description="Nutrition pillar score")
    socialConnection: int = Field(..., description="Social connection pillar score")
    emotionalWellbeing: int = Field(..., description="Emotional wellbeing pillar score")

    model_config = {
        "json_schema_extra": {
            "example": {
                "sleep": 75,
                "exercise": 80,
                "nutrition": 70,
                "socialConnection": 85,
                "emotionalWellbeing": 78,
            }
        }
    }


class SenseScoreResponse(BaseModel):
    pillars: PillarsResponse = Field(
        ..., description="Scores for each pillar of the SENSE score"
    )
    senseScore: int = Field(..., description="Overall SENSE score")

    model_config = {
        "json_schema_extra": {
            "example": {
                "pillars": {
                    "sleep": 75,
                    "exercise": 80,
                    "nutrition": 70,
                    "socialConnection": 85,
                    "emotionalWellbeing": 78,
                },
                "senseScore": 77,
            }
        }
    }


class CheckinsResponse(BaseModel):
    checkins: List[CheckinReponse] = Field(..., description="List of user checkins")
    latestCheckinTimestamp: Optional[str] = Field(
        None,
        alias="latestCheckinTimestamp",
        description="Timestamp of the latest checkin",
    )

    model_config = {
        "json_schema_extra": {
            "example": {
                "checkins": [
                    {
                        "nutrition": """I ate a balanced meal with grilled chicken,
                        quinoa, and mixed vegetables""",
                        "emotionalWellbeing": 4,
                        "socialConnection": 3,
                        "createdAt": "2024-06-13T16:22:37.892377",
                    }
                ],
                "latestCheckinTimestamp": "2024-06-13T16:22:37.892377",
            }
        }
    }


class PillarVariables(BaseModel):
    sleep: int = Field(..., description="Sleep pillar score")
    exercise: int = Field(..., description="Exercise pillar score")
    nutrition: int = Field(..., description="Nutrition pillar score")
    emotional: int = Field(..., description="Emotional wellbeing pillar score")
    social: int = Field(..., description="Social connection pillar score")
    score: int = Field(..., description="Overall SENSE score")

    model_config = {
        "json_schema_extra": {
            "example": {
                "sleep": 75,
                "exercise": 80,
                "nutrition": 70,
                "emotional": 78,
                "social": 85,
                "score": 77,
            }
        }
    }


class TypeformCaptureRequest(BaseModel):
    formId: str = Field(..., description="Typeform form ID")
    responseId: str = Field(..., description="Typeform response ID")

    model_config = {
        "json_schema_extra": {"example": {"formId": "abc123", "responseId": "def456"}}
    }
