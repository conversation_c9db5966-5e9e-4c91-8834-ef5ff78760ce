from datetime import datetime
from typing import Dict, List, Optional

from pydantic import BaseModel, Field


class Choice(BaseModel):
    id: str = Field(description="Unique identifier for the choice")
    ref: str = Field(description="Reference code for the choice")
    label: str = Field(description="Display label for the choice")


class Choices(BaseModel):
    ids: List[str] = Field(description="List of choice IDs")
    refs: Optional[List[str]] = Field(
        None, description="List of choice reference codes"
    )
    labels: Optional[List[str]] = Field(None, description="List of choice labels")
    other: Optional[str] = Field(None, description="Other choice value")


class FieldInfo(BaseModel):
    id: str = Field(description="Unique identifier for the field")
    type: str = Field(description="Type of the field")
    ref: str = Field(description="Reference code for the field")


class FieldStructure(BaseModel):
    id: str
    type: str
    ref: str
    properties: Dict
    validations: Optional[Dict] = None
    choices: Optional[Choices] = None
    title: Optional[str] = None
    attachment: Optional[Dict] = None
    layout: Optional[Dict] = None


class FormStructure(BaseModel):
    fields: List[FieldStructure]
    logic: List[Dict]
    variables: Dict[str, int]


class Answer(BaseModel):
    field: FieldInfo = Field(description="Field information for the answer")
    type: str = Field(description="Type of the answer")
    choice: Optional[Choice] = Field(
        None, description="Selected choice for single-choice questions"
    )
    choices: Optional[Choices] = Field(
        None, description="Selected choices for multiple-choice questions"
    )
    text: Optional[str] = Field(
        None, description="Text answer for open-ended questions"
    )
    number: Optional[int] = Field(
        None, description="Numeric answer for number questions"
    )
    email: Optional[str] = Field(None, description="Email answer for email questions")


class Metadata(BaseModel):
    user_agent: str = Field(description="User agent string of the respondent's browser")
    platform: str = Field(description="Platform used by the respondent")
    referer: str = Field(description="Referrer URL")
    network_id: str = Field(description="Network identifier")
    browser: str = Field(description="Browser used by the respondent")


class Variable(BaseModel):
    key: str = Field(description="Key of the variable")
    type: str = Field(description="Type of the variable")
    number: Optional[float] = Field(None, description="Numeric value of the variable")
    outcome_id: Optional[str] = Field(None, description="Associated outcome ID")


class Outcome(BaseModel):
    id: str = Field(description="Unique identifier for the outcome")
    ref: str = Field(description="Reference code for the outcome")
    title: str = Field(description="Title of the outcome")


class TypeformResponseItem(BaseModel):
    landing_id: str = Field(description="Unique identifier for the form landing")
    token: str = Field(description="Response token")
    response_id: str = Field(description="Unique identifier for the response")
    response_type: str = Field(description="Type of the response")
    landed_at: datetime = Field(
        description="Timestamp when the respondent landed on the form"
    )
    submitted_at: datetime = Field(
        description="Timestamp when the response was submitted"
    )
    metadata: Metadata = Field(description="Metadata about the response")
    hidden: Dict = Field(description="Hidden fields in the form")
    calculated: Dict[str, float] = Field(description="Calculated values")
    answers: List[Answer] = Field(description="List of answers to the form questions")
    variables: List[Variable] = Field(
        description="List of variables associated with the response"
    )

    model_config = {
        "json_schema_extra": {
            "example": {
                "landing_id": "4j3m23m4",
                "token": "8xh3jm2m7k",
                "response_id": "4k2m4l2m4",
                "response_type": "completed",
                "landed_at": "2023-06-01T10:00:00Z",
                "submitted_at": "2023-06-01T10:05:00Z",
                "metadata": {
                    "user_agent": "Mozilla/5.0...",
                    "platform": "desktop",
                    "referer": "https://example.com",
                    "network_id": "n3tw0rk1d",
                    "browser": "chrome",
                },
                "hidden": {},
                "calculated": {"score": 75},
                "answers": [
                    {
                        "field": {
                            "id": "a1b2c3",
                            "type": "multiple_choice",
                            "ref": "q1",
                        },
                        "type": "choice",
                        "choice": {"id": "d4e5f6", "ref": "ch1", "label": "Option 1"},
                    }
                ],
                "variables": [{"key": "score", "type": "number", "number": 75}],
            }
        }
    }
