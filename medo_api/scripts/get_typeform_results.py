import os
import requests

TYPEFORM_API_KEY = os.environ["TYPEFORM_API_KEY"]
TYPEFORM_FORM_ID = os.environ["TYPEFORM_FORM_ID"]


def fetch_typeform_responses():
    typeform_api_key = os.environ["TYPEFORM_API_KEY"]
    typeform_form_id = os.environ["TYPEFORM_FORM_ID"]
    base_url = "https://api.typeform.com"
    endpoint = f"/forms/{typeform_form_id}/responses"
    headers = {"Authorization": f"Bearer {typeform_api_key}"}

    response = requests.get(f"{base_url}{endpoint}", headers=headers)
    response.raise_for_status()

    return response.json()


if __name__ == "__main__":
    print(fetch_typeform_responses())


# fetch_typeform_responses()
# if responses:
#     for item in responses["items"]:
#         print(f"Response ID: {item['response_id']}")
#         for answer in item["answers"]:
#             print(f"Question: {answer['field']['type']}")
#             print(f"Answer: {answer['text']}")
#         print("---")
