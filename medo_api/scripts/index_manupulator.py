from os import environ
from sqlalchemy.orm import Session
from sqlalchemy import create_engine
from medo_api.db.crud import get_index_by_name, create_index


def set_up_session(production_db: bool = False):
    if production_db:
        db_url = environ["PRODUCTION_DATABASE_URL"].replace(
            "postgres://", "postgresql+psycopg2://"
        )
        engine = create_engine(db_url)
        return Session(bind=engine)
    else:
        db_url = environ["DATABASE_URL"].replace(
            "postgres://", "postgresql+psycopg2://"
        )
        engine = create_engine(db_url)
        return Session(bind=engine)


def copy_index_from_local_to_production_db(index_name_to_copy: str = "medo_0.5"):
    # step 1 : set up session
    production_session = set_up_session(production_db=True)
    local_session = set_up_session(production_db=False)

    # step 2 : fetch index from local env
    local_index = get_index_by_name(session=local_session, name=index_name_to_copy)
    print(f"local_index : {local_index}")
    print(f"type : {type(local_index)}")

    # step 3 : load index from local to production db
    production_index = create_index(
        session=production_session, index_to_add=local_index  # type: ignore
    )
    print(f"production_index : {production_index}")
    print(f"type : {type(production_index)}")


if __name__ == "__main__":
    # copy_index_from_local_to_production_db()
    pass
