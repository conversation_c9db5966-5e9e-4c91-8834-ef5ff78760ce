import logging
import psutil

logger = logging.getLogger(__name__)

class MemoryMonitor:
    MEMORY_THRESHOLD_MB = 1000  # 1GB threshold
    
    @staticmethod
    def log_memory_usage():
        process = psutil.Process()
        memory_info = process.memory_info()
        logger.info(
            f"Memory Usage - RSS: {memory_info.rss / 1024 / 1024:.2f}MB, "
            f"VMS: {memory_info.vms / 1024 / 1024:.2f}MB"
        )
    
    @staticmethod
    def check_memory_threshold():
        process = psutil.Process()
        memory_usage_mb = process.memory_info().rss / 1024 / 1024
        if memory_usage_mb > MemoryMonitor.MEMORY_THRESHOLD_MB:
            logger.warning(
                "memory_threshold_exceeded",
                current_usage_mb=memory_usage_mb,
                threshold_mb=MemoryMonitor.MEMORY_THRESHOLD_MB
            )
            return True
        return False
