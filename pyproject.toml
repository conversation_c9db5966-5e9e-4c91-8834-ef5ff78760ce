[tool.poetry]
name = "medo-api"
version = "1.2.7"
description = ""
authors = ["<PERSON> <<EMAIL>>"]
readme = "README.md"

[tool.poetry.dependencies]
python = "==3.10.16"
fastapi = "^0.110.1"
uvicorn = "^0.29.0"
gunicorn = "^21.2.0"
flake8 = "^7.0.0"
sqlalchemy = "^2.0.29"
psycopg2-binary = "^2.9.9"
alembic = "^1.13.1"
aiohttp = "^3.9.3"
toml = "^0.10.2"
faiss-cpu = "1.7.2"
langchain-openai = "^0.2.8"
langchain-anthropic = "^0.3.0"
langchain = "^0.3.7"
firebase-admin = "^6.5.0"
pydantic-core = "^2.4.0"
pydantic = { extras = ["email"], version = "^2.9.2" }
pandas = "^2.2.3"
openpyxl = "^3.1.5"
pyright = "^1.1.382.post0"
psutil = "^6.1.0"
cachetools = "^5.5.0"
structlog = "^24.4.0"
redis = "^5.2.0"
langchain-community = "^0.3.7"


[tool.poetry.group.dev.dependencies]
pyright = "^1.1.374"
pytest = "^8.1.1"
pytest-mock = "^3.14.0"
black = "^24.3.0"


[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.poe.tasks]
flake8 = "flake8"
pyright = "pyright"
lint = ["flake8 --exclude medo_api/db/alembic", "pyright"]
format = "black ."
test-unit = "pytest tests/unit"
test = ["test-unit", "test-integration"]
alembic-upgrade-head = "alembic upgrade head"

[tool.poe.tasks.start-dev]
shell = """
source .env.dev && uvicorn medo_api.main:app --port $PORT --reload
"""

[tool.poe.tasks.test-integration]
shell = """
source .env.integration && pytest tests/
"""
