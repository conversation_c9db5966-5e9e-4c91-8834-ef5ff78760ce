import re
import json
import pandas as pd
from os import environ
from medo_api.db.models import User
from sqlalchemy.orm import Session
from sqlalchemy import create_engine
from medo_api.db.crud import get_all_users


def set_up_session(production_db: bool = False):
    if production_db:
        db_url = environ["PRODUCTION_DATABASE_URL"].replace(
            "postgres://", "postgresql+psycopg2://"
        )
        engine = create_engine(db_url)
        return Session(bind=engine)
    else:
        db_url = environ["DATABASE_URL"].replace(
            "postgres://", "postgresql+psycopg2://"
        )
        engine = create_engine(db_url)
        return Session(bind=engine)


def users_to_json_file(users: list[User], filename: str):
    users_data = []

    for user in users:
        user_data = {
            "id": user.id,
            "firebase_uid": user.firebase_uid,
            "name": user.name,
            "email": user.email,
            "dob": user.dob.isoformat() if user.dob else None,
            "gender": user.gender,
            "created_at": user.created_at.isoformat(),
            "updated_at": user.updated_at.isoformat(),
            "chat_histories": [history.to_json() for history in user.chat_histories],
            "initial_pillars": (
                user.initial_pillars.to_json() if user.initial_pillars else None
            ),
            "checkins": [checkin.to_json() for checkin in user.checkins],
            "meal_logs": [meal_log.to_json() for meal_log in user.meal_logs],
        }
        users_data.append(user_data)

    with open(filename, "w") as f:
        json.dump(users_data, f, indent=4, default=str)
    print("all users saved to json file")


def basic_information_to_excel(json_filename: str, excel_filename: str):
    # Load the JSON data from the file
    with open(json_filename, "r") as f:
        users_data = json.load(f)

    # Extract non-list values for each user
    users_flat_data = []

    for user in users_data:
        user_flat = {
            "id": user["id"],
            "firebase_uid": user["firebase_uid"],
            "name": user["name"],
            "email": user["email"],
            "dob": user["dob"],
            "gender": user["gender"],
            "created_at": user["created_at"],
            "updated_at": user["updated_at"],
        }
        users_flat_data.append(user_flat)

    # Create a pandas DataFrame
    df = pd.DataFrame(users_flat_data)

    # Write the DataFrame to an Excel file
    df.to_excel(excel_filename, index=False)
    print("basic info table created from json file")


def chat_history_to_excel(json_filename: str, excel_filename: str):
    # Load the JSON data from the file
    with open(json_filename, "r") as f:
        users_data = json.load(f)

    # Extract chat history data
    chat_history_data = []

    for user in users_data:
        user_id = user["id"]
        user_name = user["name"]
        # Extract chat histories for each user
        for chat in user["chat_histories"]:
            chat_entry = {
                "user_id": user_id,
                "user_name": user_name,
                "createdAt": chat["createdAt"],
                "question": chat["question"],
                "answer": chat["answer"],
            }
            chat_history_data.append(chat_entry)

    # Create a pandas DataFrame
    df = pd.DataFrame(chat_history_data)

    # Write the DataFrame to an Excel file
    df.to_excel(excel_filename, index=False)
    print("chat history table created from json file")


def checkins_to_excel(json_filename: str, excel_filename: str):
    def parse_checkin_string(checkin_str: str) -> dict:
        # Extract the individual key-value pairs using regex
        match = re.findall(r"(\w+)=([^\s]+)", checkin_str)
        checkin_data = {
            key: (None if value == "None" else value) for key, value in match
        }
        return checkin_data

    # Load the JSON data from the file
    with open(json_filename, "r") as f:
        users_data = json.load(f)

    # Extract checkin data
    checkins_data = []

    for user in users_data:
        user_id = user["id"]
        user_name = user["name"]
        # Extract checkins for each user
        for checkin_str in user["checkins"]:
            checkin_data = parse_checkin_string(checkin_str)
            checkin_entry = {
                "user_id": user_id,
                "user_name": user_name,
                "nutrition": checkin_data.get("nutrition"),
                "emotionalWellbeing": checkin_data.get("emotionalWellbeing"),
                "socialConnection": checkin_data.get("socialConnection"),
                "createdAt": checkin_data.get("createdAt"),
            }
            checkins_data.append(checkin_entry)

    # Create a pandas DataFrame
    df = pd.DataFrame(checkins_data)

    # Write the DataFrame to an Excel file
    df.to_excel(excel_filename, index=False)
    print("checkins table created from json file")


def initial_pillars_to_excel(json_filename: str, excel_filename: str):
    def parse_initial_pillars_string(pillars_str: str) -> dict:
        # Extract the individual key-value pairs using regex
        match = re.findall(r"(\w+)=([^\s]+)", pillars_str)
        pillars_data = {
            key: (None if value == "None" else value) for key, value in match
        }
        return pillars_data

    # Load the JSON data from the file
    with open(json_filename, "r") as f:
        users_data = json.load(f)

    # Extract initial pillars data
    initial_pillars_data = []

    for user in users_data:
        user_id = user["id"]
        user_name = user["name"]
        initial_pillars_str = user.get("initial_pillars")
        if initial_pillars_str:
            # Parse the initial pillars string
            pillars_data = parse_initial_pillars_string(initial_pillars_str)
            pillars_entry = {
                "user_id": user_id,
                "user_name": user_name,
                "sleep": pillars_data.get("sleep"),
                "exercise": pillars_data.get("exercise"),
                "nutrition": pillars_data.get("nutrition"),
                "socialConnection": pillars_data.get("socialConnection"),
                "emotionalWellbeing": pillars_data.get("emotionalWellbeing"),
            }
            initial_pillars_data.append(pillars_entry)

    # Create a pandas DataFrame
    df = pd.DataFrame(initial_pillars_data)

    # Write the DataFrame to an Excel file
    df.to_excel(excel_filename, index=False)
    print("initial pillars table created from json file")


def meal_logs_to_excel(json_filename: str, excel_filename: str):
    def parse_meal_log_string(meal_log_str: str) -> dict:
        # Use regex to extract key-value pairs from the meal log string
        match = re.findall(r"(\w+)=('.*?'|\S+)", meal_log_str)
        meal_log_data = {key: value.strip("'") for key, value in match}

        # Remove created_at and updated_at if they exist in the data
        meal_log_data.pop("created_at", None)
        meal_log_data.pop("updated_at", None)

        return meal_log_data

    # Load the JSON data from the file
    with open(json_filename, "r") as f:
        users_data = json.load(f)

    # Extract meal logs data
    meal_logs_data = []

    for user in users_data:
        user_id = user["id"]
        user_name = user["name"]
        # Extract meal logs for each user
        for meal_log_str in user["meal_logs"]:
            meal_log_data = parse_meal_log_string(meal_log_str)
            meal_log_entry = {
                "user_id": user_id,
                "user_name": user_name,
                "meal_type": meal_log_data.get("meal_type"),
                "description": meal_log_data.get("description"),
                "is_final_meal": meal_log_data.get("is_final_meal"),
                "id": meal_log_data.get("id"),
                "analysis": meal_log_data.get("analysis"),
            }
            meal_logs_data.append(meal_log_entry)

    # Create a pandas DataFrame
    df = pd.DataFrame(meal_logs_data)

    # Write the DataFrame to an Excel file
    df.to_excel(excel_filename, index=False)
    print("meal logs table created from json file")


def load_variables_from_config(config_filename: str):
    # Load the JSON config file
    with open(config_filename, "r") as file:
        config_data = json.load(file)
    return tuple(config_data.values())


def generate_all_excel_files(
    production_db: bool = False,
    json_file_name: str = "data/all_users.json",
    basic_info_excel_filename: str = "data/all_users_basic_information.xlsx",
    chat_history_excel_filename: str = "data/all_users_chat_history.xlsx",
    checkins_excel_filename: str = "data/all_users_checkins.xlsx",
    initial_pillars_excel_filename: str = "data/all_production_users_initial_pillars.xlsx",  # noqa : E501
    meal_logs_excel_filename: str = "data/all_production_users_meal_logs.xlsx",
):
    (
        production_db,
        json_file_name,
        basic_info_excel_filename,
        chat_history_excel_filename,
        checkins_excel_filename,
        initial_pillars_excel_filename,
        meal_logs_excel_filename,
    ) = load_variables_from_config("config.json")
    # step 1 : set up session
    session = set_up_session(production_db=production_db)

    # step 2 : get all users
    all_users = get_all_users(session=session)
    print(f"Total number of users : {len(all_users)}")

    # step 3 : create a json from all users
    users_to_json_file(users=all_users, filename=json_file_name)

    # step 4 : create a basic information table from json file
    basic_information_to_excel(
        json_filename=json_file_name, excel_filename=basic_info_excel_filename
    )

    # step 5 : create a chat history table
    chat_history_to_excel(
        json_filename=json_file_name, excel_filename=chat_history_excel_filename
    )

    # step 6 : create a check in table
    checkins_to_excel(
        json_filename=json_file_name, excel_filename=checkins_excel_filename
    )

    # step 7 : create an initial pillars
    initial_pillars_to_excel(
        json_filename=json_file_name, excel_filename=initial_pillars_excel_filename
    )

    # step 8 : create a meal log table
    meal_logs_to_excel(
        json_filename=json_file_name, excel_filename=meal_logs_excel_filename
    )


if __name__ == "__main__":
    generate_all_excel_files()
