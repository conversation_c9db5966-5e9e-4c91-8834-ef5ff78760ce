#!/usr/bin/env python3
"""
Simple test script to verify the local development environment setup.
"""

import os
import sys

def test_environment_variables():
    """Test that required environment variables are set."""
    required_vars = [
        "DATABASE_URL",
        "REDISCLOUD_URL",
        "OPENAI_API_KEY",
        "ANTHROPIC_API_KEY",
        "TYPEFORM_API_KEY",
        "LANGCHAIN_API_KEY",
        "FIREBASE_PROJECT_ID",
        "FIREBASE_PRIVATE_KEY_ID",
        "FIREBASE_PRIVATE_KEY",
        "FIREBASE_CLIENT_EMAIL",
        "FIREBASE_CLIENT_ID",
        "FIREBASE_CLIENT_x509_CERT_URL",
        "BINARY_DATA_NAME_TO_USE",
    ]
    
    missing_vars = []
    for var in required_vars:
        if var not in os.environ:
            missing_vars.append(var)
    
    if missing_vars:
        print(f"❌ Missing environment variables: {', '.join(missing_vars)}")
        return False
    else:
        print("✅ All required environment variables are set")
        return True

def test_database_connection():
    """Test database connection."""
    try:
        from medo_api.db.engine import get_db_session
        from sqlalchemy import text
        session = next(get_db_session())
        session.execute(text("SELECT 1"))
        session.close()
        print("✅ Database connection successful")
        return True
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return False

def test_redis_connection():
    """Test Redis connection."""
    try:
        import redis
        redis_url = os.environ.get("REDISCLOUD_URL", "redis://localhost:6399")
        r = redis.from_url(redis_url)
        r.ping()
        print("✅ Redis connection successful")
        return True
    except Exception as e:
        print(f"❌ Redis connection failed: {e}")
        return False

def test_imports():
    """Test that key modules can be imported."""
    try:
        from medo_api.db import models
        from medo_api.utils import settings
        print("✅ Key modules imported successfully")
        return True
    except Exception as e:
        print(f"❌ Module import failed: {e}")
        return False

def main():
    """Run all tests."""
    print("🧪 Testing Medo API Local Development Environment")
    print("=" * 50)
    
    tests = [
        test_environment_variables,
        test_imports,
        test_database_connection,
        test_redis_connection,
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ Test {test.__name__} failed with exception: {e}")
            results.append(False)
        print()
    
    print("=" * 50)
    if all(results):
        print("🎉 All tests passed! Your local environment is ready.")
        return 0
    else:
        print("❌ Some tests failed. Please check the output above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
