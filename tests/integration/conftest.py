import pytest
from sqlalchemy.orm import sessionmaker
from fastapi.testclient import TestClient
from sqlalchemy.schema import DropTable
from sqlalchemy.ext.compiler import compiles

from medo_api.db.engine import db_url, create_engine
from medo_api.db.models import Base
from medo_api.main import app


@compiles(DropTable, "postgresql")
def _compile_drop_table(element, compiler, **kwargs):
    return compiler.visit_drop_table(element) + " CASCADE"


@pytest.fixture
def recreate_db():
    engine = create_engine(db_url)

    # Drop all tables with CASCADE
    Base.metadata.drop_all(engine)

    # Recreate all tables
    Base.metadata.create_all(engine)

    # Create a session
    SessionLocal = sessionmaker(bind=engine)
    session = SessionLocal()

    yield session  # provide the fixture value

    # Teardown: Close the session
    session.close()

    # Drop all tables again after the test
    Base.metadata.drop_all(engine)


@pytest.fixture
def auth_token(mocker):
    mock_verify = mocker.patch("medo_api.routers.auth.auth.verify_id_token")
    mock_verify.return_value = {
        "name": "<PERSON>",
        "email": "<EMAIL>",
        "uid": "firebase-a556",
    }
    return "mock-af45"


@pytest.fixture
def client():
    return TestClient(app)
