form_structure = {
    "fields": [
        {
            "id": "UAjSpy9q6YYo",
            "title": "How old are you?",
            "ref": "0eb9af7b-30bc-4bf8-a26c-478c5e01b95c",
            "properties": {},
            "validations": {"required": True},
            "type": "number",
        },
        {
            "id": "A9Dl0DJtEbHa",
            "title": "What sex were you assigned at birth?",
            "ref": "75fe8775-aec7-4353-9398-5e7503552d78",
            "properties": {
                "randomize": False,
                "allow_multiple_selection": False,
                "allow_other_choice": False,
                "vertical_alignment": True,
                "choices": [
                    {
                        "id": "RPm4txKzZeFo",
                        "ref": "1c33e770-467c-4e5b-9f5e-404ba70e3b16",
                        "label": "Female",
                    },
                    {
                        "id": "jW6aVQAgHLT7",
                        "ref": "c26d310f-82bc-4ec5-b842-37b7d46a25df",
                        "label": "Male",
                    },
                ],
            },
            "validations": {"required": True},
            "type": "multiple_choice",
        },
        {
            "id": "FAv4UG1MWxiL",
            "title": "What is your weight in pounds? ",
            "ref": "bfdc65a4-7c0f-460e-b805-d33e50583fd5",
            "properties": {},
            "validations": {"required": True},
            "type": "number",
        },
        {
            "id": "ZLPcX1sg2Y7o",
            "title": "What is your height in inches? ",
            "ref": "6d796d3c-444f-4cf8-8f16-8e7edffc90ee",
            "properties": {},
            "validations": {"required": True},
            "type": "number",
        },
        {
            "id": "ECzY9JdOWCxU",
            "title": "What are your top health goals?",
            "ref": "ecfbd2f2-66e9-4a8f-a0cd-997153b90d35",
            "properties": {
                "randomize": False,
                "allow_multiple_selection": True,
                "allow_other_choice": False,
                "vertical_alignment": False,
                "choices": [
                    {
                        "id": "JA1wV0J3FTNP",
                        "ref": "59180bf9-852c-4696-9f87-e8b8d3e3f01e",
                        "label": "Prolong Healthy Lifespan",
                    },
                    {
                        "id": "Wa1Q09FaBh4o",
                        "ref": "49c093e5-bfb1-4632-a1c3-13753d8be6e5",
                        "label": "Manage and Alleviate Stress",
                    },
                    {
                        "id": "jFQ7s7Z9NXGL",
                        "ref": "ace4ba5e-6489-40d5-9d76-db4ad23f9183",
                        "label": "Optimize Sleep Quality",
                    },
                    {
                        "id": "z4CTE2zppCgN",
                        "ref": "299211c3-3ded-496f-ba6a-2530b200a8e1",
                        "label": "Build Emotional Resilience and Mental Well-being",
                    },
                    {
                        "id": "FhAAJgy1ScVf",
                        "ref": "cee3729b-b82f-4372-9bd0-c8ae5a40100e",
                        "label": "Adopt a Healthier Diet (e.g., Reduce Processed Foods)",
                    },
                    {
                        "id": "SzBKqRMQMBbU",
                        "ref": "c5b987b4-0878-46f9-bcf7-dfd5080c38c3",
                        "label": "Increase Physical Activity and Exercise Routine",
                    },
                    {
                        "id": "SeBVsr4bX0wL",
                        "ref": "48047a03-2148-4706-8248-ecbc001d3662",
                        "label": "Foster Stronger Social and Community Connections",
                    },
                    {
                        "id": "QB5XVEYoGZjF",
                        "ref": "3ac372c7-1745-48c3-9b23-38b5a838bf43",
                        "label": "Enhance Daily Energy Levels",
                    },
                    {
                        "id": "74jGHZBusGWO",
                        "ref": "db546e86-6c88-4a2b-b748-92069f1acd33",
                        "label": "None of the above",
                    },
                ],
            },
            "validations": {"required": True},
            "type": "multiple_choice",
        },
        {
            "id": "mLVScXFNJuYN",
            "title": "Unlock lifetime access to your personalized results.",
            "ref": "5fcde4ee-c783-4a64-9c5c-4da9e8fb5a58",
            "properties": {
                "description": "Enter your email to save your profile and securely track your progress.",
                "fields": [
                    {
                        "id": "uo3SNBAffQ1Z",
                        "title": "Email",
                        "ref": "d0574d76-c3c0-46dc-a749-706b01b3820c",
                        "subfield_key": "email",
                        "properties": {},
                        "validations": {"required": False},
                        "type": "email",
                    }
                ],
            },
            "type": "contact_info",
        },
        {
            "id": "M2RbRs1nJOUF",
            "title": "It's great getting to know you {{field:32436086-2845-401b-95e7-a25f2ffe3c20}}! Now I want to ask the following questions to determine your Sleep habits.",
            "ref": "1fca7fcd-4261-4b6d-9664-6582ef391953",
            "properties": {"button_text": "Continue", "hide_marks": False},
            "type": "statement",
            "attachment": {
                "type": "video",
                "href": "https://www.pexels.com/video/time-lapse-footage-of-the-stars-from-light-to-dark-skies-2757419/",
            },
            "layout": {
                "type": "stack",
                "attachment": {
                    "type": "video",
                    "href": "https://www.pexels.com/video/time-lapse-footage-of-the-stars-from-light-to-dark-skies-2757419/",
                    "properties": {"brightness": -0.19},
                },
                "viewport_overrides": {
                    "small": {"type": "wallpaper"},
                    "large": {"type": "wallpaper"},
                },
            },
        },
        {
            "id": "l95cIzfQEPvd",
            "title": "How many hours of sleep do you typically get per night?",
            "ref": "3a1ee65b-5d19-455b-924c-6329d4668891",
            "properties": {
                "randomize": False,
                "allow_multiple_selection": False,
                "allow_other_choice": False,
                "vertical_alignment": True,
                "choices": [
                    {
                        "id": "jAw2OUkcQ1oT",
                        "ref": "5552fd07-eb36-44ce-a63b-2f6a8af2891c",
                        "label": "Less than 6 hours",
                    },
                    {
                        "id": "sbJgNLKRJe6I",
                        "ref": "2ea0cbb5-4606-49a4-b483-797d8e27ea3c",
                        "label": "6-7 hours",
                    },
                    {
                        "id": "GGZ8ILwuuewm",
                        "ref": "b031f9a6-7bf7-45e6-99a7-203d137018b2",
                        "label": "7-9 hours",
                    },
                    {
                        "id": "6JOJZgNL8jsH",
                        "ref": "a1a7f649-f841-4763-9fc6-cc2ae189156a",
                        "label": "More than 9 hours",
                    },
                ],
            },
            "validations": {"required": True},
            "type": "multiple_choice",
        },
        {
            "id": "czD59OrUGAmA",
            "title": "How would you rate the quality of your sleep?",
            "ref": "43e3c922-a030-4048-902e-92400bc1b58b",
            "properties": {
                "description": "Believe it or not, about 35% of adults say they get poor quality sleep.",
                "randomize": False,
                "allow_multiple_selection": False,
                "allow_other_choice": False,
                "vertical_alignment": True,
                "choices": [
                    {
                        "id": "FuKmqh1ULtR9",
                        "ref": "10808fea-6acb-42c1-974a-4d359eb6180b",
                        "label": "Poor",
                    },
                    {
                        "id": "bOVw1YeShM7B",
                        "ref": "81dc8c4f-8c32-4d3b-961b-b1a93278d6b8",
                        "label": "Fair",
                    },
                    {
                        "id": "AweXpoVH6K15",
                        "ref": "165ec207-492d-4a9c-81c0-d6e828c3c5ea",
                        "label": "Good",
                    },
                    {
                        "id": "pyHLzDGGfnBs",
                        "ref": "48162ad5-f834-400e-b2cb-5956e0e6a3aa",
                        "label": "Excellent",
                    },
                ],
            },
            "validations": {"required": True},
            "type": "multiple_choice",
        },
        {
            "id": "DP6y4ssLQc7R",
            "title": "How consistent is your sleep schedule?",
            "ref": "e04b8d02-f6df-4d42-b743-accffd3c4b1c",
            "properties": {
                "randomize": False,
                "allow_multiple_selection": False,
                "allow_other_choice": False,
                "vertical_alignment": True,
                "choices": [
                    {
                        "id": "VuiEle0k0M0a",
                        "ref": "314813d8-5275-4c98-b779-9c54f09b7592",
                        "label": "Not consistent at all",
                    },
                    {
                        "id": "47ytz0FqrgVU",
                        "ref": "4d90b94f-b494-4877-b091-6ec8d6b0b678",
                        "label": "Somewhat consistent",
                    },
                    {
                        "id": "avQxtAZzaGNI",
                        "ref": "e1885d40-5d9e-4ccf-a1ee-8e6676eaea62",
                        "label": "Very consistent",
                    },
                ],
            },
            "validations": {"required": True},
            "type": "multiple_choice",
        },
        {
            "id": "8XPXQKzRPuAL",
            "title": "How would you describe your sleep environment (e.g., noise, light, comfort)?",
            "ref": "30f60f49-86ad-45ef-9956-5feb13a0107c",
            "properties": {
                "randomize": False,
                "allow_multiple_selection": False,
                "allow_other_choice": False,
                "vertical_alignment": True,
                "choices": [
                    {
                        "id": "nymXJG5w15xS",
                        "ref": "3d670fa5-619e-4f4f-895a-61fc5d0aff86",
                        "label": "Ideal",
                    },
                    {
                        "id": "vCxoZW1bJ3xP",
                        "ref": "760df6d5-5cc1-4e06-9e00-1d472fbf50ee",
                        "label": "Mostly good, with occasional disturbances",
                    },
                    {
                        "id": "91cmjIbzaBWM",
                        "ref": "7972d587-7fa0-4791-9ec8-3234b47eec5f",
                        "label": "Moderate, with some regular disturbances",
                    },
                    {
                        "id": "yhC8gshaSBbr",
                        "ref": "912a1cab-80e4-4367-a979-671b4998e176",
                        "label": "Poor, with frequent disturbances",
                    },
                ],
            },
            "validations": {"required": True},
            "type": "multiple_choice",
        },
        {
            "id": "z5bR1OYJbXmX",
            "title": "Physical exercise is a cornerstone of longevity, playing a critical role in extending not only the lifespan but also enhancing the quality of life, lets calculate yours! ",
            "ref": "441f526b-e8dd-4edb-8398-cd6276410993",
            "properties": {"button_text": "Continue", "hide_marks": False},
            "type": "statement",
            "attachment": {
                "type": "video",
                "href": "https://www.pexels.com/video/a-man-running-on-the-beach-shore-3125907/",
            },
            "layout": {
                "type": "stack",
                "attachment": {
                    "type": "video",
                    "href": "https://www.pexels.com/video/a-man-running-on-the-beach-shore-3125907/",
                    "properties": {"brightness": -0.42},
                },
                "viewport_overrides": {
                    "small": {"type": "wallpaper"},
                    "large": {"type": "wallpaper"},
                },
            },
        },
        {
            "id": "AoiyCVXwjXnN",
            "title": "How many days per week do you engage in moderate to vigorous physical activity?",
            "ref": "0759c2c6-d5e9-46b5-b917-84fbd8409c53",
            "properties": {
                "description": "Only 1 in 5 adults meet the recommended exercise guidelines.",
                "randomize": False,
                "allow_multiple_selection": False,
                "allow_other_choice": False,
                "vertical_alignment": True,
                "choices": [
                    {
                        "id": "u2V4eYicuTdQ",
                        "ref": "c83a8499-4be9-494d-bfbd-7bdb0b9b414c",
                        "label": "0-1 days",
                    },
                    {
                        "id": "cNASfI4DvENz",
                        "ref": "584a568a-846a-4ae7-8993-24ceeeb5cdc9",
                        "label": "2-3 days",
                    },
                    {
                        "id": "LlUyE3jyYeEa",
                        "ref": "9091f502-996f-4168-9bd3-435eaa4ac597",
                        "label": "4-5 days",
                    },
                    {
                        "id": "ymtLypPxrIp5",
                        "ref": "d80271bd-1dfc-4eab-81aa-01acbca0c536",
                        "label": "6-7 days",
                    },
                ],
            },
            "validations": {"required": True},
            "type": "multiple_choice",
        },
        {
            "id": "d5XmDT1X8q4e",
            "title": "How do you balance high and low-intensity workouts in your routine?",
            "ref": "106b59b9-c0c3-42c8-9c99-a15fda331f2a",
            "properties": {
                "description": "A balanced mix of high and low-intensity workouts \ncan be beneficial for overall fitness",
                "randomize": False,
                "allow_multiple_selection": False,
                "allow_other_choice": False,
                "vertical_alignment": True,
                "choices": [
                    {
                        "id": "KjIQ7kG6Waqz",
                        "ref": "b242cfd0-a3da-4d43-a946-053c2c269a5e",
                        "label": "I mostly engage in high-intensity workouts with little or no low-intensity activities",
                    },
                    {
                        "id": "K959mDfdZ1RC",
                        "ref": "0f8d5fdc-0692-4b48-a977-9091c3aad2bd",
                        "label": "I mostly engage in low-intensity workouts with little or no high-intensity activities",
                    },
                    {
                        "id": "HVyFatNvx3zF",
                        "ref": "a3feab7e-5479-434b-bba7-ddd506e040e8",
                        "label": "I incorporate a balanced mix of high and low-intensity workouts",
                    },
                    {
                        "id": "o8EiYuyxaZFy",
                        "ref": "4c8d5572-a0a5-4e07-91c6-89290c7f2ce7",
                        "label": "I engage in high or low-intensity workouts but not regularly",
                    },
                ],
            },
            "validations": {"required": True},
            "type": "multiple_choice",
        },
        {
            "id": "H0fyitLraTDd",
            "title": "How consistent are you with your exercise routine, and do you feel you’re progressing in your fitness goals",
            "ref": "50700a06-92e8-4500-93ab-a616b96c3994",
            "properties": {
                "description": "People who track their workouts are 2 times more likely to hit their fitness goals. ",
                "randomize": False,
                "allow_multiple_selection": False,
                "allow_other_choice": False,
                "vertical_alignment": True,
                "choices": [
                    {
                        "id": "baLpfM59XDIA",
                        "ref": "7933a189-a62c-43a4-b091-2dd186e07406",
                        "label": "Very consistent, with noticeable progress",
                    },
                    {
                        "id": "IFK7ReiLasFu",
                        "ref": "0cfb31e6-43a0-471b-aac9-38c7a0cdd264",
                        "label": "Somewhat consistent, with occasional progress",
                    },
                    {
                        "id": "DjSEoZwTwjPP",
                        "ref": "4444b498-eb59-4c7a-a4fa-686ce8c4f42a",
                        "label": "Inconsistent, with little to no progress",
                    },
                    {
                        "id": "GEk2Zw457AMZ",
                        "ref": "3b5c7503-307d-4447-97d9-dd83ab1791c9",
                        "label": "Not consistent at all",
                    },
                ],
            },
            "validations": {"required": True},
            "type": "multiple_choice",
        },
        {
            "id": "8OY8c1hOVDCP",
            "title": "How often do you allow your body to fully recover between workouts?",
            "ref": "b89b2519-d081-4ea3-b9e2-f93446ade6b5",
            "properties": {
                "description": "Recovery is an essential aspect of physical fitness that impacts overall well-being.",
                "randomize": False,
                "allow_multiple_selection": False,
                "allow_other_choice": False,
                "vertical_alignment": True,
                "choices": [
                    {
                        "id": "lNnvFFVSCdEz",
                        "ref": "b5b53d88-ef8c-4a60-a39c-913627a577b3",
                        "label": "Rarely, I push through without recovery",
                    },
                    {
                        "id": "LVFgS3eEfZd5",
                        "ref": "7a50e932-1b49-492c-8cb2-17cd4e35491f",
                        "label": "Sometimes, but I often skip recovery",
                    },
                    {
                        "id": "KXOIz9yWsIGA",
                        "ref": "44b44290-ed34-45b0-b07f-2f06e341a574",
                        "label": "Often, but not consistently",
                    },
                    {
                        "id": "Kep1s8ltaFxt",
                        "ref": "8b01383a-95e9-4e8d-aaaa-7a630437c8f8",
                        "label": "Always, with dedicated recovery time",
                    },
                ],
            },
            "validations": {"required": True},
            "type": "multiple_choice",
        },
        {
            "id": "10UHfmgiRd0b",
            "title": "Amazing {{field:32436086-2845-401b-95e7-a25f2ffe3c20}}. Next up I want to focus on your nutrition and eating habits.",
            "ref": "ccf6c895-fda2-4438-a2be-ac3dbcf2f090",
            "properties": {"button_text": "Continue", "hide_marks": False},
            "type": "statement",
            "attachment": {
                "type": "video",
                "href": "https://www.pexels.com/video/people-about-to-eat-3255109/",
            },
            "layout": {
                "type": "stack",
                "attachment": {
                    "type": "video",
                    "href": "https://www.pexels.com/video/people-about-to-eat-3255109/",
                    "properties": {"brightness": -0.42},
                },
                "viewport_overrides": {
                    "small": {"type": "wallpaper"},
                    "large": {"type": "wallpaper"},
                },
            },
        },
        {
            "id": "7Cv0MyntFf3H",
            "title": "How would you describe your typical daily diet?",
            "ref": "c3eadf68-1e29-47bb-8381-b005ac1e9b24",
            "properties": {
                "randomize": False,
                "allow_multiple_selection": False,
                "allow_other_choice": False,
                "vertical_alignment": True,
                "choices": [
                    {
                        "id": "Bh2joSziOTeY",
                        "ref": "c29518bc-f5a1-4d41-9d14-9c73ba223ace",
                        "label": "Mostly processed foods",
                    },
                    {
                        "id": "lTvTkJcBXUHT",
                        "ref": "208e120c-85a9-4b42-a774-858a6b8b19d7",
                        "label": "Balance of whole and processed foods",
                    },
                    {
                        "id": "CNjhPxO7YmDh",
                        "ref": "77f7d90c-6dc3-4ff5-bbec-3f641f421a66",
                        "label": "Mostly whole foods",
                    },
                    {
                        "id": "IFp2O807fml9",
                        "ref": "e4f21f0c-cb9b-47eb-bd0f-fdcdaed6b06c",
                        "label": "Mostly plant-based foods",
                    },
                ],
            },
            "validations": {"required": True},
            "type": "multiple_choice",
        },
        {
            "id": "wt5h13CacMmg",
            "title": "How many glasses of water do you drink daily?",
            "ref": "2675681c-d2b1-453e-bb08-54a7d1816ffa",
            "properties": {
                "description": "The average person should drink 8 glasses of water a day, but nearly 75% of us are dehydrated!",
                "randomize": False,
                "allow_multiple_selection": False,
                "allow_other_choice": False,
                "vertical_alignment": True,
                "choices": [
                    {
                        "id": "0ajwaEe8tqtk",
                        "ref": "32e07c27-42ed-41ba-b3c3-5dccd8d21635",
                        "label": "Less than 4",
                    },
                    {
                        "id": "6iYkNSzy6sox",
                        "ref": "a4dc32e7-3435-4b0f-8498-4d00858b730d",
                        "label": "4-6",
                    },
                    {
                        "id": "bHvMkyY01t9W",
                        "ref": "2ed81ec6-d5b7-4b84-a50a-580a068ff229",
                        "label": "7-9",
                    },
                    {
                        "id": "iT74T8kIy8oZ",
                        "ref": "730bf577-bea1-4c74-88a2-48c248028c7a",
                        "label": "More than 9",
                    },
                ],
            },
            "validations": {"required": True},
            "type": "multiple_choice",
        },
        {
            "id": "OYP6nZUrKGIa",
            "title": "Do you follow any specific dietary approach?",
            "ref": "d1ceaa3d-30ee-46d2-9459-80a42904013e",
            "properties": {
                "randomize": False,
                "allow_multiple_selection": False,
                "allow_other_choice": False,
                "vertical_alignment": False,
                "choices": [
                    {
                        "id": "cJN2SSyh3FeN",
                        "ref": "08959dfa-f019-4ca7-ba47-7b82b31d17f6",
                        "label": "No specific approach",
                    },
                    {
                        "id": "tJUeZqk4J2Cw",
                        "ref": "36922b69-0a18-4b48-a204-eb34bf1ba9f1",
                        "label": "Low-fat",
                    },
                    {
                        "id": "GjERjhBRLrh8",
                        "ref": "65a00047-ca70-4ded-a6f7-89514378786a",
                        "label": "Low-carb",
                    },
                    {
                        "id": "2okLpJ78nZG1",
                        "ref": "6d63f861-a072-46d8-9bb5-f52278f62968",
                        "label": "Mediterranean",
                    },
                    {
                        "id": "27GO6dxkNeBE",
                        "ref": "b827b896-4aa4-4daf-947e-1d4548add029",
                        "label": "Vegetarian",
                    },
                    {
                        "id": "B9d1JDnd4GGp",
                        "ref": "76b13f3b-b856-4046-973c-c7d32e56a71a",
                        "label": "Vegan",
                    },
                    {
                        "id": "sYVKfQl34Eto",
                        "ref": "fa587b55-6b32-424d-8ac2-6fd0f4cd65e5",
                        "label": "High protein",
                    },
                    {
                        "id": "1UcljQJBc1gB",
                        "ref": "8aa173ce-d6f3-4a03-b61c-16ade7b436cf",
                        "label": "Currently imbalanced",
                    },
                    {
                        "id": "rl5VMtDJD5uL",
                        "ref": "1f3b0510-596f-4b4b-a275-4773a3bc1440",
                        "label": "Intermittent fasting",
                    },
                ],
            },
            "validations": {"required": True},
            "type": "multiple_choice",
        },
        {
            "id": "yPk6GJReMlu3",
            "title": "On average, how many alcoholic drinks do you consume per week?",
            "ref": "b95e39c8-7076-4adc-82b7-743e43687734",
            "properties": {
                "description": "Over 25% of adults drink more than the recommended amount.",
                "randomize": False,
                "allow_multiple_selection": False,
                "allow_other_choice": False,
                "vertical_alignment": True,
                "choices": [
                    {
                        "id": "865eSktcUqLS",
                        "ref": "285b3b62-9c49-4487-80ff-92ee5e29adcc",
                        "label": "More than 10",
                    },
                    {
                        "id": "fW8bOfRmV6Pt",
                        "ref": "0ba5dcef-ec8f-4f7a-b6e9-e48014681a81",
                        "label": "7-10",
                    },
                    {
                        "id": "wXJKh4BYlStd",
                        "ref": "e8eba323-6a5a-4524-b4b2-54c87ccc47c9",
                        "label": "4-6",
                    },
                    {
                        "id": "G9JTD5gLXqJc",
                        "ref": "44f21c16-cc9b-4a67-b143-bb18e758260e",
                        "label": "1-3",
                    },
                    {
                        "id": "ouMXVaBLixt4",
                        "ref": "bd0bf844-ed34-465a-ab9b-0bad90c6ebca",
                        "label": "0",
                    },
                ],
            },
            "validations": {"required": True},
            "type": "multiple_choice",
        },
        {
            "id": "Hh9WBeKqSZMK",
            "title": "Time to explore the rhythm of your social connections!",
            "ref": "6434bfef-5773-44fc-a6c1-b8bfbf84ec45",
            "properties": {"button_text": "Continue", "hide_marks": False},
            "type": "statement",
            "attachment": {
                "type": "video",
                "href": "https://www.pexels.com/video/people-in-a-party-toasting-each-other-3188889/",
            },
            "layout": {
                "type": "stack",
                "attachment": {
                    "type": "video",
                    "href": "https://www.pexels.com/video/people-in-a-party-toasting-each-other-3188889/",
                    "properties": {"brightness": -0.51},
                },
                "viewport_overrides": {
                    "small": {"type": "wallpaper"},
                    "large": {"type": "wallpaper"},
                },
            },
        },
        {
            "id": "o1g5Ag5KYha1",
            "title": "How often do you engage in social activities with friends, family, or community groups?",
            "ref": "fb8b5038-50a6-48ee-9b19-2ff8fcc3209f",
            "properties": {
                "description": "Did you know that social interaction can increase your lifespan? \nSocial activities could include meeting friends for coffee, attending community events, or family gatherings.",
                "randomize": False,
                "allow_multiple_selection": False,
                "allow_other_choice": False,
                "vertical_alignment": True,
                "choices": [
                    {
                        "id": "LkIDRfFuCsxW",
                        "ref": "47749110-ac76-4241-afeb-51af8514b1fa",
                        "label": "Rarely",
                    },
                    {
                        "id": "kXFOwZbAPIHo",
                        "ref": "c9ccf549-5147-4667-a665-24fb3db2a9b0",
                        "label": "A few times per month",
                    },
                    {
                        "id": "B6i1BaMCz1Mn",
                        "ref": "d8ff3506-45bb-4685-8363-d23776fa0f8c",
                        "label": "1-2 times per week",
                    },
                    {
                        "id": "OrfkuLU8diuA",
                        "ref": "f6b73d89-c01a-4bec-8256-32391a29964d",
                        "label": "More than 2 times per week",
                    },
                ],
            },
            "validations": {"required": True},
            "type": "multiple_choice",
        },
        {
            "id": "AnjiuHk4fY6c",
            "title": "How often do you feel lonely or isolated?",
            "ref": "2dd55285-e399-482a-bcc2-6735c8b3f920",
            "properties": {
                "description": "Nearly 1 in 5 adults report feeling lonely.",
                "randomize": False,
                "allow_multiple_selection": False,
                "allow_other_choice": False,
                "vertical_alignment": True,
                "choices": [
                    {
                        "id": "hbHOjU7hItwi",
                        "ref": "c450275c-a235-46d2-a2d2-860c73497351",
                        "label": "Often",
                    },
                    {
                        "id": "cWqTSFoNTb8O",
                        "ref": "f0373cc4-10d9-4dd5-8834-dcecd20a5687",
                        "label": "Sometimes",
                    },
                    {
                        "id": "BVG2FvSjwAg4",
                        "ref": "8d5410f3-22c1-42bd-a311-a00cb19fd4c4",
                        "label": "Rarely",
                    },
                    {
                        "id": "z2RfmDtwB8Pt",
                        "ref": "bae47e96-d239-4410-a3b1-5b348ad128a4",
                        "label": "Never",
                    },
                ],
            },
            "validations": {"required": True},
            "type": "multiple_choice",
        },
        {
            "id": "8TZQnT6Iv4nO",
            "title": "Do you have a strong support network of people you can rely on?",
            "ref": "37f43dc2-fbed-4007-be1e-d2e704ced61d",
            "properties": {
                "description": "Having a strong support network can boost your health.",
                "randomize": False,
                "allow_multiple_selection": False,
                "allow_other_choice": False,
                "vertical_alignment": True,
                "choices": [
                    {
                        "id": "CiGRXmQiG6w4",
                        "ref": "10b89f8f-c265-4e52-be9f-4fb010a5e65e",
                        "label": "No",
                    },
                    {
                        "id": "zutVzZZGNc0I",
                        "ref": "89b0cbef-1795-4c69-8c84-c6390fe43c77",
                        "label": "Somewhat",
                    },
                    {
                        "id": "U2eEbzDdE17N",
                        "ref": "a5ee8528-db84-40dc-bd56-06da1bfce3e2",
                        "label": "Yes",
                    },
                ],
            },
            "validations": {"required": True},
            "type": "multiple_choice",
        },
        {
            "id": "JVSvHwAkT9S8",
            "title": "How strong are your relationships with friends and family?",
            "ref": "a9978155-d312-456b-920a-4138ad0b7b1e",
            "properties": {
                "description": "Close relationships are key to happiness, \nbut 1 in 10 people report feeling disconnected.",
                "randomize": False,
                "allow_multiple_selection": False,
                "allow_other_choice": False,
                "vertical_alignment": True,
                "choices": [
                    {
                        "id": "L6jRPq28mq0u",
                        "ref": "d7c4365e-71fb-4f14-9539-34e8195dec70",
                        "label": "Weak",
                    },
                    {
                        "id": "lisrMYFf1rsS",
                        "ref": "199a0157-68f7-4935-a34e-fd1c67e10c76",
                        "label": "Moderate",
                    },
                    {
                        "id": "3obCkvM30uVV",
                        "ref": "01d585be-a9db-4e47-90c7-6c39919ed7d2",
                        "label": "Strong",
                    },
                ],
            },
            "validations": {"required": True},
            "type": "multiple_choice",
        },
        {
            "id": "zkZYYfvzXX8K",
            "title": "Now, let’s dive into the flow of your emotional well-being!",
            "ref": "6ba9da3b-5708-4cbd-b17c-1c86977e9208",
            "properties": {"button_text": "Continue", "hide_marks": False},
            "type": "statement",
            "attachment": {
                "type": "video",
                "href": "https://www.pexels.com/video/a-person-doing-meditation-exercises-on-the-mountain-side-2796307/",
            },
            "layout": {
                "type": "stack",
                "attachment": {
                    "type": "video",
                    "href": "https://www.pexels.com/video/a-person-doing-meditation-exercises-on-the-mountain-side-2796307/",
                },
                "viewport_overrides": {
                    "small": {"type": "wallpaper"},
                    "large": {"type": "wallpaper"},
                },
            },
        },
        {
            "id": "XQtMPAOdaixw",
            "title": "How would you rate your overall mood on a typical day?",
            "ref": "b325df62-b561-464c-b846-5480803c4e8b",
            "properties": {
                "description": "About 1 in 3 people report being in a good mood most days.",
                "randomize": False,
                "allow_multiple_selection": False,
                "allow_other_choice": False,
                "vertical_alignment": True,
                "choices": [
                    {
                        "id": "DYkNQgKzUySW",
                        "ref": "59d3e451-84b9-43c4-aa65-b2dffe275062",
                        "label": "Poor",
                    },
                    {
                        "id": "6okuTi6kRnSq",
                        "ref": "1badfccd-0012-440a-b1d2-4884f502f154",
                        "label": "Fair",
                    },
                    {
                        "id": "LP8olavqmAzJ",
                        "ref": "43dce41d-adcc-4202-87e6-a5718302f8d0",
                        "label": "Good",
                    },
                    {
                        "id": "f5RGqlTh9eHc",
                        "ref": "89dbf0c9-a433-4347-992a-8fd7fcb06da7",
                        "label": "Excellent",
                    },
                ],
            },
            "validations": {"required": True},
            "type": "multiple_choice",
        },
        {
            "id": "VWAwOLN4PuvA",
            "title": "How often do you feel stressed or anxious?",
            "ref": "d2744aa5-2f72-44a9-9ed3-5b2ecc4cb327",
            "properties": {
                "description": "Stress is a part of life, but 1 in 5 people experience it regularly.",
                "randomize": False,
                "allow_multiple_selection": False,
                "allow_other_choice": False,
                "vertical_alignment": True,
                "choices": [
                    {
                        "id": "Y3oO4UYnnU8E",
                        "ref": "2537b822-f0fe-4a97-b137-22e181ab336a",
                        "label": "Often",
                    },
                    {
                        "id": "ySqO1ybDNand",
                        "ref": "8f50a8c0-c9a9-4ebb-89bc-efcf5a66c864",
                        "label": "Sometimes",
                    },
                    {
                        "id": "7LlsZ7SWEgcw",
                        "ref": "fddba980-223d-4134-bf98-9da86c5278d1",
                        "label": "Rarely",
                    },
                    {
                        "id": "noFhlS1IxZU2",
                        "ref": "ad2385a1-284a-4520-a697-e9e5563dbf4b",
                        "label": "Never",
                    },
                ],
            },
            "validations": {"required": True},
            "type": "multiple_choice",
        },
        {
            "id": "9Z0XZzUO4sCi",
            "title": "How often do you engage in hobbies or activities you enjoy?",
            "ref": "f46a1097-7100-445f-865c-16a81552722c",
            "properties": {
                "description": "People who regularly engage in hobbies are happier and less stressed.",
                "randomize": False,
                "allow_multiple_selection": False,
                "allow_other_choice": False,
                "vertical_alignment": True,
                "choices": [
                    {
                        "id": "yCFVjx4tOsjq",
                        "ref": "0317fd9c-51fa-4496-af45-a4416c8a9c5c",
                        "label": "Rarely",
                    },
                    {
                        "id": "LvX8Mvn01XJD",
                        "ref": "455d3e41-a59b-41a9-a990-f871978ace0b",
                        "label": "A few times per month",
                    },
                    {
                        "id": "GQfQIqHYBTZa",
                        "ref": "49dba2e1-b74e-4740-9eed-7772695b7889",
                        "label": "1-2 times per week",
                    },
                    {
                        "id": "vtFcDtYBfHio",
                        "ref": "b6b5c8e9-9f11-4d5f-ab86-f6881aa4e409",
                        "label": "More than 2 times per week",
                    },
                ],
            },
            "validations": {"required": True},
            "type": "multiple_choice",
        },
        {
            "id": "Gt9gMOj4FUyN",
            "title": "How often do you engage in hobbies or activities you enjoy?",
            "ref": "8f733407-bf55-4ec8-8101-2566221e9d94",
            "properties": {
                "description": "Only 1 in 4 people have a go-to strategy for coping with stress.",
                "randomize": False,
                "allow_multiple_selection": False,
                "allow_other_choice": False,
                "vertical_alignment": True,
                "choices": [
                    {
                        "id": "N5VZELBINdEm",
                        "ref": "ecbadaef-565c-45ee-84e5-35f5b855c03d",
                        "label": "Poorly, with high stress and negative emotions",
                    },
                    {
                        "id": "gemjBFiZAOSy",
                        "ref": "8206dcb6-b259-4859-8b29-97cc44d6b30c",
                        "label": "Somewhat effectively, with moderate stress",
                    },
                    {
                        "id": "nTMfuHTIv93w",
                        "ref": "8bcf8ebf-69f5-4d0a-96ff-b2171499040d",
                        "label": "Very effectively, with resilience and adaptability",
                    },
                ],
            },
            "validations": {"required": True},
            "type": "multiple_choice",
        },
        {
            "id": "ResOSJcmUzTg",
            "title": "You're almost there! Just a few more clicks to unlock your path to longevity! 🎉 Keep going, your future self will thank you!",
            "ref": "edb158be-3374-427c-b02c-f86f65d7164e",
            "properties": {"button_text": "Continue", "hide_marks": False},
            "type": "statement",
            "attachment": {
                "type": "video",
                "href": "https://www.pexels.com/video/woman-walking-in-the-middle-of-the-road-3018669/",
            },
            "layout": {
                "type": "stack",
                "attachment": {
                    "type": "video",
                    "href": "https://www.pexels.com/video/woman-walking-in-the-middle-of-the-road-3018669/",
                    "properties": {"brightness": -0.42},
                },
                "viewport_overrides": {
                    "small": {"type": "wallpaper"},
                    "large": {"type": "wallpaper"},
                },
            },
        },
        {
            "id": "e5E6L9w6QT93",
            "title": "Do you take any supplements (e.g., vitamins, minerals, herbal supplements)?",
            "ref": "8cf1b139-e37b-4813-aa5b-2be0472b0244",
            "properties": {
                "description": "Over half of adults take supplements daily.",
                "randomize": False,
                "allow_multiple_selection": True,
                "allow_other_choice": True,
                "vertical_alignment": False,
                "choices": [
                    {
                        "id": "kYOi1I1kndqP",
                        "ref": "4b1ce195-904a-4900-9221-b92c2394a33e",
                        "label": "Multivitamins",
                    },
                    {
                        "id": "3nDqElyiNBVl",
                        "ref": "fb5cb5ce-9abc-4c8b-811b-c0333304e0cc",
                        "label": "Vitamin C",
                    },
                    {
                        "id": "U3FmUaicu9xy",
                        "ref": "7783cb15-f353-491e-9a15-b256a6589f55",
                        "label": "Vitamin D",
                    },
                    {
                        "id": "qEmK1w3uxDAF",
                        "ref": "1b25cfed-c698-465f-a04e-7c0fb3b2f130",
                        "label": "Calcium",
                    },
                    {
                        "id": "v8fZ9XpYUQ1a",
                        "ref": "5c2434fb-c54d-443c-b71f-777207bc7253",
                        "label": "Fish Oil (Omega-3 fatty acids)",
                    },
                    {
                        "id": "9q403kXBldOI",
                        "ref": "84ba413a-1d19-4918-ab51-173e8811cbb6",
                        "label": "Probiotics",
                    },
                    {
                        "id": "htyC0aaSxAJ0",
                        "ref": "4dfeb4f2-ff3c-4e58-b204-f9bdd90d6fb9",
                        "label": "Magnesium",
                    },
                    {
                        "id": "u5m72tlLJv1V",
                        "ref": "bab5919a-bc11-484b-8f38-1ef92cbd60b9",
                        "label": "Zinc",
                    },
                    {
                        "id": "gB4ubxrlGkxJ",
                        "ref": "25e8a387-f82c-4ce8-ab1b-caa7075ccc28",
                        "label": "Fiber supplements",
                    },
                    {
                        "id": "Vp0igUMZZZA0",
                        "ref": "a912702a-decb-4088-bb46-a87711659d78",
                        "label": "Iron",
                    },
                    {
                        "id": "ixGUeRXButZU",
                        "ref": "78c3852e-7e14-403f-8b4d-8fb0ecdb5ee8",
                        "label": "Protein (powder or shake)",
                    },
                    {
                        "id": "2SE9BrDKbn1q",
                        "ref": "6fb2b740-3799-4173-9646-b13de6788a46",
                        "label": "Creatine",
                    },
                    {
                        "id": "8NThYuP22SJM",
                        "ref": "0c0303f1-bc72-4f05-9d41-77df9a5e5132",
                        "label": "Turmeric/Curcumin",
                    },
                ],
            },
            "validations": {"required": True},
            "type": "multiple_choice",
        },
        {
            "id": "ygb3yam2HLYk",
            "title": "Do you smoke cigarettes, vape or use tobacco products?",
            "ref": "6ccab87c-656f-4651-8ff7-88e447184be6",
            "properties": {
                "randomize": False,
                "allow_multiple_selection": False,
                "allow_other_choice": False,
                "vertical_alignment": True,
                "choices": [
                    {
                        "id": "MURBtgu3apCM",
                        "ref": "2c327106-ddc2-46bc-be46-fac89e708f32",
                        "label": "Daily",
                    },
                    {
                        "id": "DOY0LqxejdyA",
                        "ref": "4b28b077-0449-4e14-9657-c03cb7582c51",
                        "label": "Occasionally",
                    },
                    {
                        "id": "Jj1bk59ulooW",
                        "ref": "44f5424d-92ae-4bae-b5b1-9c623a3e619b",
                        "label": "Never",
                    },
                ],
            },
            "validations": {"required": True},
            "type": "multiple_choice",
        },
        {
            "id": "L048iF3u9FXy",
            "title": "How often do you see a doctor for a routine check-up?",
            "ref": "8828ba49-dd95-46a9-9b56-c4cff529f2a6",
            "properties": {
                "description": "Regular check-ups can catch problems early, yet nearly 1 in 3 people skip them.",
                "randomize": False,
                "allow_multiple_selection": False,
                "allow_other_choice": False,
                "vertical_alignment": True,
                "choices": [
                    {
                        "id": "NaE7ELxzpb0D",
                        "ref": "8603ef91-0b7d-4e16-aaf7-d1ea54d834e5",
                        "label": "Rarely",
                    },
                    {
                        "id": "5gyYxmDPyO9S",
                        "ref": "df2907ec-604a-47e6-a03d-4c5840eb41d2",
                        "label": "Once a year",
                    },
                    {
                        "id": "gX7xTjN6VTAn",
                        "ref": "d1ae9c15-93cb-4528-8429-80967b4fff51",
                        "label": "More than once a year",
                    },
                ],
            },
            "validations": {"required": True},
            "type": "multiple_choice",
        },
        {
            "id": "MGqtGbi4JGhU",
            "title": "What is your typical blood pressure reading?",
            "ref": "1426290f-b875-4f32-bf82-9c02d33306a0",
            "properties": {
                "description": "High blood pressure affects 1 in 3 adults.",
                "randomize": False,
                "allow_multiple_selection": False,
                "allow_other_choice": False,
                "vertical_alignment": True,
                "choices": [
                    {
                        "id": "FtcIimmgkWvW",
                        "ref": "8706c866-aa52-4cb4-b866-dee464ea3336",
                        "label": "High (140/90 or higher)",
                    },
                    {
                        "id": "LCROGgHaJ17E",
                        "ref": "ab7f7f99-83f1-45fd-8a59-9b2fbc9e820c",
                        "label": "Normal (120/80 - 139/89)",
                    },
                    {
                        "id": "XV66FJg7022A",
                        "ref": "378b9903-8e61-4624-9e92-474e1e86d8bb",
                        "label": "Optimal (Below 120/80)",
                    },
                    {
                        "id": "8HlieXdr88Vr",
                        "ref": "011a34d6-a48e-48c4-9b13-fb5604fdd0da",
                        "label": "I don't know",
                    },
                ],
            },
            "validations": {"required": True},
            "type": "multiple_choice",
        },
        {
            "id": "tjJXjAIJBZ5L",
            "title": "Have you been diagnosed with diabetes?",
            "ref": "0820241d-6dfb-437d-a9bf-739698d2c1d8",
            "properties": {
                "randomize": False,
                "allow_multiple_selection": False,
                "allow_other_choice": False,
                "vertical_alignment": True,
                "choices": [
                    {
                        "id": "0KUPFEkaPrnG",
                        "ref": "c538b8d4-02dc-49af-8f0e-50e00826e9e7",
                        "label": "Yes",
                    },
                    {
                        "id": "WxTYH9SrsayV",
                        "ref": "0b37b911-593c-469e-b566-d56129262476",
                        "label": "No",
                    },
                ],
            },
            "validations": {"required": True},
            "type": "multiple_choice",
        },
        {
            "id": "kcQ3BpUmKuZp",
            "title": "Do you experience chronic pain?",
            "ref": "0e71749d-304d-462a-a303-51ae7c802cff",
            "properties": {
                "description": "Chronic pain affects nearly 20% of adults.",
                "randomize": False,
                "allow_multiple_selection": False,
                "allow_other_choice": False,
                "vertical_alignment": True,
                "choices": [
                    {
                        "id": "jAXVxFKI2nKG",
                        "ref": "9dab2b8f-5810-4b6d-ae9b-af7f6835a937",
                        "label": "Yes, frequently",
                    },
                    {
                        "id": "KavAVOBaHJMO",
                        "ref": "f44d6f7a-3c2f-4f9f-91e1-1b352928b62c",
                        "label": "Occasionally",
                    },
                    {
                        "id": "7P4Jg1JsHkvl",
                        "ref": "1e7b3356-3b8b-4c89-82da-11660cc5f012",
                        "label": "Rarely or never",
                    },
                ],
            },
            "validations": {"required": True},
            "type": "multiple_choice",
        },
        {
            "id": "TSzwwQ2AfM5E",
            "title": "Do you have any parents or grandparents who are older than 90 years old?",
            "ref": "9a95fd3b-ffa6-41ca-a05a-86c294aa3a3c",
            "properties": {
                "randomize": False,
                "allow_multiple_selection": False,
                "allow_other_choice": False,
                "vertical_alignment": True,
                "choices": [
                    {
                        "id": "iI8DagWIffoo",
                        "ref": "d26a52cf-520f-4ea0-b917-fdfee58c51ac",
                        "label": "Yes",
                    },
                    {
                        "id": "3SnboZTBSLPK",
                        "ref": "08388fdc-9ef1-42cf-b60b-0c1fcf0fa270",
                        "label": "No",
                    },
                ],
            },
            "validations": {"required": True},
            "type": "multiple_choice",
        },
        {
            "id": "VPJ1I34X0q0J",
            "title": "Optional: if you upload your most recent bloodwork, we can personalize your SENSE Score.\nWe support PDF, DOCX, and JPG formats. Thank you for helping us tailor our assessment to your needs!",
            "ref": "22ddcb6d-4a91-4104-b35a-61272e1d2e49",
            "properties": {},
            "validations": {"required": False},
            "type": "file_upload",
        },
        {
            "id": "vDRvOBIAHK1E",
            "title": "Please select the activities you like to do and want to continue over the next 10 years.",
            "ref": "5de464b8-d146-42e7-8d9f-a19f6f033c27",
            "properties": {
                "randomize": False,
                "allow_multiple_selection": True,
                "allow_other_choice": False,
                "vertical_alignment": False,
                "choices": [
                    {
                        "id": "QtXlBYBcz8sa",
                        "ref": "567d402d-6acd-44c5-abb7-e3420f02ebe0",
                        "label": "Running or jogging",
                    },
                    {
                        "id": "ipjW1j7DhghP",
                        "ref": "ba5c6bce-78ec-47ad-82cf-114c268143e1",
                        "label": "Cycling (road, mountain, or stationary)",
                    },
                    {
                        "id": "GsUYcJCwOK43",
                        "ref": "3aad4dce-1bab-4f53-8202-05fb9d1d4432",
                        "label": "Hiking",
                    },
                    {
                        "id": "l20G8h93yoqD",
                        "ref": "1756b220-b784-41fd-a69a-7169dafbdba5",
                        "label": "Swimming",
                    },
                    {
                        "id": "Buu7FN5Ru1bH",
                        "ref": "a130e1e9-1081-441a-a09c-84e8e24c3ebd",
                        "label": "Tennis",
                    },
                    {
                        "id": "SYETjkHuubY2",
                        "ref": "58ead178-db91-4e5c-8a5e-15c88f7a5152",
                        "label": "Walking",
                    },
                    {
                        "id": "3enL0kKCZbwQ",
                        "ref": "06f6b963-0f5d-43b4-9ca7-cc47f64b9ccd",
                        "label": "Weight training",
                    },
                    {
                        "id": "2P0VxFzFVczw",
                        "ref": "f7f3ca97-238e-4e7c-a0b8-f0ff8103500c",
                        "label": "Golf",
                    },
                    {
                        "id": "qCzq62muv6wy",
                        "ref": "39e91a6e-3204-4322-9b6c-e6ee86c20e07",
                        "label": "Yoga",
                    },
                    {
                        "id": "NeKsyoAvKBHt",
                        "ref": "0411cbaf-e0db-4ab0-846f-8f061480a74b",
                        "label": "Pilates",
                    },
                    {
                        "id": "4B9oJyXom5zZ",
                        "ref": "d8b46060-1d15-4c54-bde1-dacce7068dc5",
                        "label": "Martial arts (karate, judo, taekwondo, etc.)",
                    },
                    {
                        "id": "9SHunoCp3mUp",
                        "ref": "516d4b78-cc5b-410a-ae00-5c320a7684f8",
                        "label": "Dance classes (ballet, ballroom, hip-hop, etc.)",
                    },
                    {
                        "id": "2qv2mbgkGRzP",
                        "ref": "a70104e5-aab5-4749-ba20-5d8e96ac9f60",
                        "label": "Tai Chi",
                    },
                    {
                        "id": "PmwyLR1UH5yS",
                        "ref": "f23e8d76-9e66-477e-8d8b-8041071e0b92",
                        "label": "Pickleball",
                    },
                    {
                        "id": "Iz3DjGTU656i",
                        "ref": "3dae5695-e945-4849-9c92-19e265fd64f9",
                        "label": "Basketball",
                    },
                    {
                        "id": "8XHMFbT5BiYG",
                        "ref": "81e10bdc-6867-4b63-9641-4e59e130dcb0",
                        "label": "Volleyball",
                    },
                    {
                        "id": "oe6ejX2fArpr",
                        "ref": "a765cad8-b5d6-47f9-b8ca-f7d7d032aa5f",
                        "label": "Nordic (XC) skiing",
                    },
                    {
                        "id": "scGNs9TqqyGJ",
                        "ref": "a432a99d-09e1-4489-aa2f-6fb99603db12",
                        "label": "Kayaking or canoeing",
                    },
                    {
                        "id": "b7Cn01KSl8XD",
                        "ref": "a0d1fc55-7b24-4cd8-a1f6-212681076abb",
                        "label": "Soccer",
                    },
                    {
                        "id": "1rt3M8Rm3HAr",
                        "ref": "11bcaeb4-91ab-4472-bfe1-879d48341e52",
                        "label": "Sailing",
                    },
                    {
                        "id": "3V96anZUVZiB",
                        "ref": "9531a040-c2c8-415b-b8b6-781e6dc94e65",
                        "label": "Surfing",
                    },
                    {
                        "id": "NRcxfzqOF6R1",
                        "ref": "50524518-759a-452f-8c68-4496642076b2",
                        "label": "Snowboarding",
                    },
                    {
                        "id": "qVFKOfkWiEvV",
                        "ref": "117c8693-e158-4ee4-802a-931cf8819113",
                        "label": "Alpine Skiing",
                    },
                    {
                        "id": "hxxZbzmIxxyY",
                        "ref": "6d2a8fbe-0aed-4531-b163-d18f83a88997",
                        "label": "Ice skating",
                    },
                    {
                        "id": "YxqioblE5f8d",
                        "ref": "e1f8a03f-ea3c-4111-a94f-2acb1f38606e",
                        "label": "Roller skating or rollerblading",
                    },
                    {
                        "id": "zQFiKDswnKxn",
                        "ref": "a02c91c4-9622-4fdb-a43e-c80b008b3cc7",
                        "label": "Bowling",
                    },
                    {
                        "id": "lxQ7t9AOEXnT",
                        "ref": "0e2c786e-74ea-4fd3-a7db-5d87f363e832",
                        "label": "Horseback riding",
                    },
                    {
                        "id": "ehaosPeZRpQB",
                        "ref": "60c625e4-5e18-4e0d-a9e0-c277bd2efbb4",
                        "label": "Gardening or yard work",
                    },
                    {
                        "id": "HeYlESpMgcCb",
                        "ref": "8a92fc50-f5e5-4b7c-a82f-ef90da33e817",
                        "label": "Rock climbing",
                    },
                    {
                        "id": "9w8SL3JtFb77",
                        "ref": "ccd176b3-3aa9-4c6a-830e-edd928f26336",
                        "label": "Reading",
                    },
                    {
                        "id": "YpnmKruBuHiB",
                        "ref": "a63589b4-7635-4ac0-9177-6ab6d7035757",
                        "label": "Writing (journal, poetry, fiction, etc.)",
                    },
                    {
                        "id": "vVoPlMkZeFnm",
                        "ref": "6e4318ad-8cad-464d-a87b-dc090a7e2fe8",
                        "label": "Painting, drawing, or sketching",
                    },
                    {
                        "id": "5SCN5tCmWlhw",
                        "ref": "8d1c7d97-1ca1-4878-a1d9-7578841c76b0",
                        "label": "Playing a musical instrument",
                    },
                    {
                        "id": "LYZR9HXp6iNO",
                        "ref": "95b5bb03-b95c-4375-abfb-0ddf1d8705e8",
                        "label": "Singing or participating in a choir",
                    },
                    {
                        "id": "zDMOPBJIyQDv",
                        "ref": "539430b1-a46e-4f60-b5c2-ba43533060e2",
                        "label": "Cooking or baking",
                    },
                    {
                        "id": "g4EH5DDqLJk1",
                        "ref": "f4539604-ea2e-428c-9fac-715d2e8358fd",
                        "label": "Knitting, crocheting, or needlework",
                    },
                    {
                        "id": "TwgYklN6W3q0",
                        "ref": "e58fb414-ccae-416e-8dc2-79d097cd3734",
                        "label": "Woodworking or carpentry",
                    },
                    {
                        "id": "AxW6hY33a4oM",
                        "ref": "d21deca0-fe5d-4a44-9cad-20f03d6cc8c8",
                        "label": "Pottery or ceramics",
                    },
                    {
                        "id": "Z7wKIUErIiB5",
                        "ref": "0dd44758-63d6-4714-9cbe-d7323befcae0",
                        "label": "Jewelry making",
                    },
                    {
                        "id": "lqlAD9EkUiNQ",
                        "ref": "f0322279-290d-4e30-b961-97229562052e",
                        "label": "Learning a new language",
                    },
                    {
                        "id": "McKTJaPSCP2q",
                        "ref": "d754a173-50bf-4a44-9bc5-43e8cc72deb8",
                        "label": "Solving puzzles (crosswords, Sudoku, jigsaw puzzles, etc.)",
                    },
                    {
                        "id": "uY3g9kzKcV9c",
                        "ref": "1747cfd6-226a-4838-a898-15e91c8b7cb4",
                        "label": "Attending cultural events (concerts, plays, museums, etc.)",
                    },
                    {
                        "id": "h2xAkuIjHxw4",
                        "ref": "18657882-eeeb-49a2-bb53-fab29395c25d",
                        "label": "Playing board games or card games",
                    },
                    {
                        "id": "0B2XMW5Do4Hl",
                        "ref": "e24a1417-1681-49d8-899f-75f75b4b887f",
                        "label": "Collecting (stamps, coins, antiques, etc.)",
                    },
                    {
                        "id": "rVisinMFY2ut",
                        "ref": "0fc549de-65a9-4a76-83ee-c7ad4a767e02",
                        "label": "Traveling or exploring new places",
                    },
                    {
                        "id": "I11mtUTJWthZ",
                        "ref": "08a2b3d5-97c8-4d83-8620-58d6530cda10",
                        "label": "Volunteering or community service",
                    },
                    {
                        "id": "sm2vZvJ8gfwd",
                        "ref": "869c0e2a-60f2-4f7a-b0f4-0acceac31251",
                        "label": "Acting or participating in community theater",
                    },
                    {
                        "id": "iCi5VXLysXVm",
                        "ref": "b78c14fa-f58c-4f98-a7b8-42a262786598",
                        "label": "Genealogy or family history research",
                    },
                    {
                        "id": "DB0yflTkhL6b",
                        "ref": "664f6868-c0ce-46d2-9daf-304b000a8e27",
                        "label": "Astronomy or stargazing",
                    },
                    {
                        "id": "svOjlgpKqCZx",
                        "ref": "7a9b15b6-66ab-4e00-ad39-23c228884d9b",
                        "label": "Bird watching or nature observation",
                    },
                    {
                        "id": "S9A2Rq0ighFf",
                        "ref": "7a1b1c71-e823-4210-9025-9627fa7bcf96",
                        "label": "Meditation or mindfulness practice",
                    },
                    {
                        "id": "p7jZJxJzG5vM",
                        "ref": "9b2295e8-4908-44a4-83db-9d45d39cf3c4",
                        "label": "Attending sporting events as a spectator",
                    },
                    {
                        "id": "we91hhw71Z3p",
                        "ref": "680130fd-8e6f-4347-8042-efc29514ac20",
                        "label": "Home improvement projects",
                    },
                    {
                        "id": "QaADN7G2UlmV",
                        "ref": "16308e5c-a3a5-40d1-8a00-5eb03fdea3c2",
                        "label": "Participating in book clubs or discussion groups",
                    },
                    {
                        "id": "doReWyubxOqQ",
                        "ref": "cbd022cc-7568-47c9-8bfb-6a551bf784d5",
                        "label": "Shopping with friends",
                    },
                    {
                        "id": "zJodcdCYouAJ",
                        "ref": "3b3b3cdf-4526-4eba-8ce0-0ebc5c5d8b6b",
                        "label": "Wine tasting or brewing beer",
                    },
                    {
                        "id": "VsL40HCMdPo6",
                        "ref": "b60eecf0-e305-47c0-a385-38bf14f0d876",
                        "label": "Participating in online courses or workshops",
                    },
                    {
                        "id": "LYVX0MjHvL9p",
                        "ref": "c9127a11-67ea-403e-b680-4d0e47496d6e",
                        "label": "Playing video games or online gaming",
                    },
                    {
                        "id": "fyCAQAsTQsDA",
                        "ref": "ac800068-c947-42d9-81e2-5cf197192628",
                        "label": "Being sexually active",
                    },
                    {
                        "id": "7jIYc9Rc0HWY",
                        "ref": "71a87179-5c6f-4af4-92e0-ffbfc2189d61",
                        "label": "Driving",
                    },
                ],
            },
            "validations": {"required": True},
            "type": "multiple_choice",
        },
        {
            "id": "JgHHuT5VcdGC",
            "title": "Please rank the following aspects of your ideal aging experience from most important (1) to least important (7)",
            "ref": "699092a2-b000-4269-b5e4-674a66190d0d",
            "properties": {
                "randomize": False,
                "allow_multiple_selection": True,
                "choices": [
                    {
                        "id": "6Qoahb0Kxopr",
                        "ref": "29146437-cb5c-4557-8a9e-1fe94f60a709",
                        "label": "Maintaining physical health and function",
                    },
                    {
                        "id": "x5oGJVi9wg5c",
                        "ref": "26ba0ae9-355d-4b04-a70a-42e61d87e45a",
                        "label": "Nurturing close relationships and social engagement",
                    },
                    {
                        "id": "ZuinxjXkPsZ7",
                        "ref": "0e2b6784-197c-4a47-ab76-6bbe832b9ac2",
                        "label": "Pursuing personal growth and new experiences",
                    },
                    {
                        "id": "5spXz5Iarfna",
                        "ref": "d52f7aaa-658d-42bc-a65a-361cec9187dd",
                        "label": "Cultivating emotional resilience and inner peace",
                    },
                    {
                        "id": "4xjIEC3woVEH",
                        "ref": "f915742a-4f50-49e0-bb82-cd05d09f295c",
                        "label": "Enjoying leisure, rest, and life’s simple pleasures",
                    },
                ],
            },
            "validations": {"required": False},
            "type": "ranking",
        },
        {
            "id": "3ULuL1WUdQ3E",
            "title": "Thank you for taking the quiz. \nYour SENSE Score will be sent to your email. ",
            "ref": "103613cd-df8e-4ca0-8bef-934e712e502b",
            "properties": {
                "description": "Be on the look out for it! ",
                "button_text": "Continue",
                "hide_marks": False,
            },
            "type": "statement",
        },
    ],
    "variables": {
        "emotional": 0,
        "exercise": 0,
        "nutrition": 0,
        "score": 0,
        "sensescore": 0,
        "sleep": 0,
        "social": 0,
    },
    "logic": [
        {
            "type": "field",
            "ref": "730aca31-6aef-48a9-8429-b85c107e7149",
            "actions": [
                {
                    "action": "jump",
                    "details": {
                        "to": {
                            "type": "field",
                            "value": "9d4ed188-4fce-41b9-b74e-a06f730666a3",
                        }
                    },
                    "condition": {
                        "op": "is",
                        "vars": [
                            {
                                "type": "field",
                                "value": "730aca31-6aef-48a9-8429-b85c107e7149",
                            },
                            {
                                "type": "choice",
                                "value": "5b827f23-0af3-444a-875d-e3e54241151b",
                            },
                        ],
                    },
                },
                {
                    "action": "jump",
                    "details": {
                        "to": {
                            "type": "field",
                            "value": "32436086-2845-401b-95e7-a25f2ffe3c20",
                        }
                    },
                    "condition": {"op": "always", "vars": []},
                },
            ],
        },
        {
            "type": "field",
            "ref": "43e3c922-a030-4048-902e-92400bc1b58b",
            "actions": [
                {
                    "action": "add",
                    "details": {
                        "target": {"type": "variable", "value": "sleep"},
                        "value": {"type": "constant", "value": 2},
                    },
                    "condition": {
                        "op": "is",
                        "vars": [
                            {
                                "type": "field",
                                "value": "43e3c922-a030-4048-902e-92400bc1b58b",
                            },
                            {
                                "type": "choice",
                                "value": "81dc8c4f-8c32-4d3b-961b-b1a93278d6b8",
                            },
                        ],
                    },
                },
                {
                    "action": "add",
                    "details": {
                        "target": {"type": "variable", "value": "sleep"},
                        "value": {"type": "constant", "value": 4},
                    },
                    "condition": {
                        "op": "is",
                        "vars": [
                            {
                                "type": "field",
                                "value": "43e3c922-a030-4048-902e-92400bc1b58b",
                            },
                            {
                                "type": "choice",
                                "value": "165ec207-492d-4a9c-81c0-d6e828c3c5ea",
                            },
                        ],
                    },
                },
                {
                    "action": "add",
                    "details": {
                        "target": {"type": "variable", "value": "sleep"},
                        "value": {"type": "constant", "value": 5},
                    },
                    "condition": {
                        "op": "is",
                        "vars": [
                            {
                                "type": "field",
                                "value": "43e3c922-a030-4048-902e-92400bc1b58b",
                            },
                            {
                                "type": "choice",
                                "value": "48162ad5-f834-400e-b2cb-5956e0e6a3aa",
                            },
                        ],
                    },
                },
            ],
        },
        {
            "type": "field",
            "ref": "e04b8d02-f6df-4d42-b743-accffd3c4b1c",
            "actions": [
                {
                    "action": "add",
                    "details": {
                        "target": {"type": "variable", "value": "sleep"},
                        "value": {"type": "constant", "value": 1},
                    },
                    "condition": {
                        "op": "is",
                        "vars": [
                            {
                                "type": "field",
                                "value": "e04b8d02-f6df-4d42-b743-accffd3c4b1c",
                            },
                            {
                                "type": "choice",
                                "value": "4d90b94f-b494-4877-b091-6ec8d6b0b678",
                            },
                        ],
                    },
                },
                {
                    "action": "add",
                    "details": {
                        "target": {"type": "variable", "value": "sleep"},
                        "value": {"type": "constant", "value": 3},
                    },
                    "condition": {
                        "op": "is",
                        "vars": [
                            {
                                "type": "field",
                                "value": "e04b8d02-f6df-4d42-b743-accffd3c4b1c",
                            },
                            {
                                "type": "choice",
                                "value": "e1885d40-5d9e-4ccf-a1ee-8e6676eaea62",
                            },
                        ],
                    },
                },
            ],
        },
        {
            "type": "field",
            "ref": "30f60f49-86ad-45ef-9956-5feb13a0107c",
            "actions": [
                {
                    "action": "add",
                    "details": {
                        "target": {"type": "variable", "value": "sleep"},
                        "value": {"type": "constant", "value": 2},
                    },
                    "condition": {
                        "op": "is",
                        "vars": [
                            {
                                "type": "field",
                                "value": "30f60f49-86ad-45ef-9956-5feb13a0107c",
                            },
                            {
                                "type": "choice",
                                "value": "3d670fa5-619e-4f4f-895a-61fc5d0aff86",
                            },
                        ],
                    },
                },
                {
                    "action": "add",
                    "details": {
                        "target": {"type": "variable", "value": "sleep"},
                        "value": {"type": "constant", "value": 1},
                    },
                    "condition": {
                        "op": "is",
                        "vars": [
                            {
                                "type": "field",
                                "value": "30f60f49-86ad-45ef-9956-5feb13a0107c",
                            },
                            {
                                "type": "choice",
                                "value": "760df6d5-5cc1-4e06-9e00-1d472fbf50ee",
                            },
                        ],
                    },
                },
                {
                    "action": "add",
                    "details": {
                        "target": {"type": "variable", "value": "sleep"},
                        "value": {"type": "constant", "value": 1},
                    },
                    "condition": {
                        "op": "is",
                        "vars": [
                            {
                                "type": "field",
                                "value": "30f60f49-86ad-45ef-9956-5feb13a0107c",
                            },
                            {
                                "type": "choice",
                                "value": "3d670fa5-619e-4f4f-895a-61fc5d0aff86",
                            },
                        ],
                    },
                },
            ],
        },
        {
            "type": "field",
            "ref": "0759c2c6-d5e9-46b5-b917-84fbd8409c53",
            "actions": [
                {
                    "action": "add",
                    "details": {
                        "target": {"type": "variable", "value": "exercise"},
                        "value": {"type": "constant", "value": 3},
                    },
                    "condition": {
                        "op": "is",
                        "vars": [
                            {
                                "type": "field",
                                "value": "0759c2c6-d5e9-46b5-b917-84fbd8409c53",
                            },
                            {
                                "type": "choice",
                                "value": "584a568a-846a-4ae7-8993-24ceeeb5cdc9",
                            },
                        ],
                    },
                },
                {
                    "action": "add",
                    "details": {
                        "target": {"type": "variable", "value": "exercise"},
                        "value": {"type": "constant", "value": 6},
                    },
                    "condition": {
                        "op": "is",
                        "vars": [
                            {
                                "type": "field",
                                "value": "0759c2c6-d5e9-46b5-b917-84fbd8409c53",
                            },
                            {
                                "type": "choice",
                                "value": "9091f502-996f-4168-9bd3-435eaa4ac597",
                            },
                        ],
                    },
                },
                {
                    "action": "add",
                    "details": {
                        "target": {"type": "variable", "value": "exercise"},
                        "value": {"type": "constant", "value": 8},
                    },
                    "condition": {
                        "op": "is",
                        "vars": [
                            {
                                "type": "field",
                                "value": "0759c2c6-d5e9-46b5-b917-84fbd8409c53",
                            },
                            {
                                "type": "choice",
                                "value": "d80271bd-1dfc-4eab-81aa-01acbca0c536",
                            },
                        ],
                    },
                },
            ],
        },
        {
            "type": "field",
            "ref": "106b59b9-c0c3-42c8-9c99-a15fda331f2a",
            "actions": [
                {
                    "action": "add",
                    "details": {
                        "target": {"type": "variable", "value": "exercise"},
                        "value": {"type": "constant", "value": 2},
                    },
                    "condition": {
                        "op": "is",
                        "vars": [
                            {
                                "type": "field",
                                "value": "106b59b9-c0c3-42c8-9c99-a15fda331f2a",
                            },
                            {
                                "type": "choice",
                                "value": "b242cfd0-a3da-4d43-a946-053c2c269a5e",
                            },
                        ],
                    },
                },
                {
                    "action": "add",
                    "details": {
                        "target": {"type": "variable", "value": "exercise"},
                        "value": {"type": "constant", "value": 2},
                    },
                    "condition": {
                        "op": "is",
                        "vars": [
                            {
                                "type": "field",
                                "value": "106b59b9-c0c3-42c8-9c99-a15fda331f2a",
                            },
                            {
                                "type": "choice",
                                "value": "0f8d5fdc-0692-4b48-a977-9091c3aad2bd",
                            },
                        ],
                    },
                },
                {
                    "action": "add",
                    "details": {
                        "target": {"type": "variable", "value": "exercise"},
                        "value": {"type": "constant", "value": 6},
                    },
                    "condition": {
                        "op": "is",
                        "vars": [
                            {
                                "type": "field",
                                "value": "106b59b9-c0c3-42c8-9c99-a15fda331f2a",
                            },
                            {
                                "type": "choice",
                                "value": "a3feab7e-5479-434b-bba7-ddd506e040e8",
                            },
                        ],
                    },
                },
                {
                    "action": "add",
                    "details": {
                        "target": {"type": "variable", "value": "exercise"},
                        "value": {"type": "constant", "value": 1},
                    },
                    "condition": {
                        "op": "is",
                        "vars": [
                            {
                                "type": "field",
                                "value": "106b59b9-c0c3-42c8-9c99-a15fda331f2a",
                            },
                            {
                                "type": "choice",
                                "value": "4c8d5572-a0a5-4e07-91c6-89290c7f2ce7",
                            },
                        ],
                    },
                },
            ],
        },
        {
            "type": "field",
            "ref": "50700a06-92e8-4500-93ab-a616b96c3994",
            "actions": [
                {
                    "action": "add",
                    "details": {
                        "target": {"type": "variable", "value": "exercise"},
                        "value": {"type": "constant", "value": 4},
                    },
                    "condition": {
                        "op": "is",
                        "vars": [
                            {
                                "type": "field",
                                "value": "50700a06-92e8-4500-93ab-a616b96c3994",
                            },
                            {
                                "type": "choice",
                                "value": "7933a189-a62c-43a4-b091-2dd186e07406",
                            },
                        ],
                    },
                },
                {
                    "action": "add",
                    "details": {
                        "target": {"type": "variable", "value": "exercise"},
                        "value": {"type": "constant", "value": 2},
                    },
                    "condition": {
                        "op": "is",
                        "vars": [
                            {
                                "type": "field",
                                "value": "50700a06-92e8-4500-93ab-a616b96c3994",
                            },
                            {
                                "type": "choice",
                                "value": "0cfb31e6-43a0-471b-aac9-38c7a0cdd264",
                            },
                        ],
                    },
                },
                {
                    "action": "add",
                    "details": {
                        "target": {"type": "variable", "value": "exercise"},
                        "value": {"type": "constant", "value": 1},
                    },
                    "condition": {
                        "op": "is",
                        "vars": [
                            {
                                "type": "field",
                                "value": "50700a06-92e8-4500-93ab-a616b96c3994",
                            },
                            {
                                "type": "choice",
                                "value": "4444b498-eb59-4c7a-a4fa-686ce8c4f42a",
                            },
                        ],
                    },
                },
            ],
        },
        {
            "type": "field",
            "ref": "b89b2519-d081-4ea3-b9e2-f93446ade6b5",
            "actions": [
                {
                    "action": "add",
                    "details": {
                        "target": {"type": "variable", "value": "exercise"},
                        "value": {"type": "constant", "value": 1},
                    },
                    "condition": {
                        "op": "is",
                        "vars": [
                            {
                                "type": "field",
                                "value": "b89b2519-d081-4ea3-b9e2-f93446ade6b5",
                            },
                            {
                                "type": "choice",
                                "value": "7a50e932-1b49-492c-8cb2-17cd4e35491f",
                            },
                        ],
                    },
                },
                {
                    "action": "add",
                    "details": {
                        "target": {"type": "variable", "value": "exercise"},
                        "value": {"type": "constant", "value": 1},
                    },
                    "condition": {
                        "op": "is",
                        "vars": [
                            {
                                "type": "field",
                                "value": "b89b2519-d081-4ea3-b9e2-f93446ade6b5",
                            },
                            {
                                "type": "choice",
                                "value": "44b44290-ed34-45b0-b07f-2f06e341a574",
                            },
                        ],
                    },
                },
                {
                    "action": "add",
                    "details": {
                        "target": {"type": "variable", "value": "exercise"},
                        "value": {"type": "constant", "value": 2},
                    },
                    "condition": {
                        "op": "is",
                        "vars": [
                            {
                                "type": "field",
                                "value": "b89b2519-d081-4ea3-b9e2-f93446ade6b5",
                            },
                            {
                                "type": "choice",
                                "value": "8b01383a-95e9-4e8d-aaaa-7a630437c8f8",
                            },
                        ],
                    },
                },
            ],
        },
        {
            "type": "field",
            "ref": "c3eadf68-1e29-47bb-8381-b005ac1e9b24",
            "actions": [
                {
                    "action": "add",
                    "details": {
                        "target": {"type": "variable", "value": "nutrition"},
                        "value": {"type": "constant", "value": 5},
                    },
                    "condition": {
                        "op": "is",
                        "vars": [
                            {
                                "type": "field",
                                "value": "c3eadf68-1e29-47bb-8381-b005ac1e9b24",
                            },
                            {
                                "type": "choice",
                                "value": "208e120c-85a9-4b42-a774-858a6b8b19d7",
                            },
                        ],
                    },
                },
                {
                    "action": "add",
                    "details": {
                        "target": {"type": "variable", "value": "nutrition"},
                        "value": {"type": "constant", "value": 10},
                    },
                    "condition": {
                        "op": "is",
                        "vars": [
                            {
                                "type": "field",
                                "value": "c3eadf68-1e29-47bb-8381-b005ac1e9b24",
                            },
                            {
                                "type": "choice",
                                "value": "77f7d90c-6dc3-4ff5-bbec-3f641f421a66",
                            },
                        ],
                    },
                },
                {
                    "action": "add",
                    "details": {
                        "target": {"type": "variable", "value": "nutrition"},
                        "value": {"type": "constant", "value": 10},
                    },
                    "condition": {
                        "op": "is",
                        "vars": [
                            {
                                "type": "field",
                                "value": "c3eadf68-1e29-47bb-8381-b005ac1e9b24",
                            },
                            {
                                "type": "choice",
                                "value": "e4f21f0c-cb9b-47eb-bd0f-fdcdaed6b06c",
                            },
                        ],
                    },
                },
            ],
        },
        {
            "type": "field",
            "ref": "d1ceaa3d-30ee-46d2-9459-80a42904013e",
            "actions": [
                {
                    "action": "add",
                    "details": {
                        "target": {"type": "variable", "value": "nutrition"},
                        "value": {"type": "constant", "value": 2},
                    },
                    "condition": {
                        "op": "is",
                        "vars": [
                            {
                                "type": "field",
                                "value": "d1ceaa3d-30ee-46d2-9459-80a42904013e",
                            },
                            {
                                "type": "choice",
                                "value": "08959dfa-f019-4ca7-ba47-7b82b31d17f6",
                            },
                        ],
                    },
                },
                {
                    "action": "add",
                    "details": {
                        "target": {"type": "variable", "value": "nutrition"},
                        "value": {"type": "constant", "value": 4},
                    },
                    "condition": {
                        "op": "is",
                        "vars": [
                            {
                                "type": "field",
                                "value": "d1ceaa3d-30ee-46d2-9459-80a42904013e",
                            },
                            {
                                "type": "choice",
                                "value": "36922b69-0a18-4b48-a204-eb34bf1ba9f1",
                            },
                        ],
                    },
                },
                {
                    "action": "add",
                    "details": {
                        "target": {"type": "variable", "value": "nutrition"},
                        "value": {"type": "constant", "value": 4},
                    },
                    "condition": {
                        "op": "is",
                        "vars": [
                            {
                                "type": "field",
                                "value": "d1ceaa3d-30ee-46d2-9459-80a42904013e",
                            },
                            {
                                "type": "choice",
                                "value": "65a00047-ca70-4ded-a6f7-89514378786a",
                            },
                        ],
                    },
                },
                {
                    "action": "add",
                    "details": {
                        "target": {"type": "variable", "value": "nutrition"},
                        "value": {"type": "constant", "value": 5},
                    },
                    "condition": {
                        "op": "is",
                        "vars": [
                            {
                                "type": "field",
                                "value": "d1ceaa3d-30ee-46d2-9459-80a42904013e",
                            },
                            {
                                "type": "choice",
                                "value": "6d63f861-a072-46d8-9bb5-f52278f62968",
                            },
                        ],
                    },
                },
                {
                    "action": "add",
                    "details": {
                        "target": {"type": "variable", "value": "nutrition"},
                        "value": {"type": "constant", "value": 4},
                    },
                    "condition": {
                        "op": "is",
                        "vars": [
                            {
                                "type": "field",
                                "value": "d1ceaa3d-30ee-46d2-9459-80a42904013e",
                            },
                            {
                                "type": "choice",
                                "value": "b827b896-4aa4-4daf-947e-1d4548add029",
                            },
                        ],
                    },
                },
                {
                    "action": "add",
                    "details": {
                        "target": {"type": "variable", "value": "nutrition"},
                        "value": {"type": "constant", "value": 4},
                    },
                    "condition": {
                        "op": "is",
                        "vars": [
                            {
                                "type": "field",
                                "value": "d1ceaa3d-30ee-46d2-9459-80a42904013e",
                            },
                            {
                                "type": "choice",
                                "value": "76b13f3b-b856-4046-973c-c7d32e56a71a",
                            },
                        ],
                    },
                },
                {
                    "action": "add",
                    "details": {
                        "target": {"type": "variable", "value": "nutrition"},
                        "value": {"type": "constant", "value": 4},
                    },
                    "condition": {
                        "op": "is",
                        "vars": [
                            {
                                "type": "field",
                                "value": "d1ceaa3d-30ee-46d2-9459-80a42904013e",
                            },
                            {
                                "type": "choice",
                                "value": "1f3b0510-596f-4b4b-a275-4773a3bc1440",
                            },
                        ],
                    },
                },
                {
                    "action": "add",
                    "details": {
                        "target": {"type": "variable", "value": "nutrition"},
                        "value": {"type": "constant", "value": 4},
                    },
                    "condition": {
                        "op": "is",
                        "vars": [
                            {
                                "type": "field",
                                "value": "d1ceaa3d-30ee-46d2-9459-80a42904013e",
                            },
                            {
                                "type": "choice",
                                "value": "fa587b55-6b32-424d-8ac2-6fd0f4cd65e5",
                            },
                        ],
                    },
                },
            ],
        },
        {
            "type": "field",
            "ref": "2675681c-d2b1-453e-bb08-54a7d1816ffa",
            "actions": [
                {
                    "action": "add",
                    "details": {
                        "target": {"type": "variable", "value": "nutrition"},
                        "value": {"type": "constant", "value": 1},
                    },
                    "condition": {
                        "op": "is",
                        "vars": [
                            {
                                "type": "field",
                                "value": "2675681c-d2b1-453e-bb08-54a7d1816ffa",
                            },
                            {
                                "type": "choice",
                                "value": "a4dc32e7-3435-4b0f-8498-4d00858b730d",
                            },
                        ],
                    },
                },
                {
                    "action": "add",
                    "details": {
                        "target": {"type": "variable", "value": "nutrition"},
                        "value": {"type": "constant", "value": 3},
                    },
                    "condition": {
                        "op": "is",
                        "vars": [
                            {
                                "type": "field",
                                "value": "2675681c-d2b1-453e-bb08-54a7d1816ffa",
                            },
                            {
                                "type": "choice",
                                "value": "2ed81ec6-d5b7-4b84-a50a-580a068ff229",
                            },
                        ],
                    },
                },
                {
                    "action": "add",
                    "details": {
                        "target": {"type": "variable", "value": "exercise"},
                        "value": {"type": "constant", "value": 3},
                    },
                    "condition": {
                        "op": "is",
                        "vars": [
                            {
                                "type": "field",
                                "value": "2675681c-d2b1-453e-bb08-54a7d1816ffa",
                            },
                            {
                                "type": "choice",
                                "value": "730bf577-bea1-4c74-88a2-48c248028c7a",
                            },
                        ],
                    },
                },
            ],
        },
        {
            "type": "field",
            "ref": "b95e39c8-7076-4adc-82b7-743e43687734",
            "actions": [
                {
                    "action": "add",
                    "details": {
                        "target": {"type": "variable", "value": "nutrition"},
                        "value": {"type": "constant", "value": 1},
                    },
                    "condition": {
                        "op": "is",
                        "vars": [
                            {
                                "type": "field",
                                "value": "b95e39c8-7076-4adc-82b7-743e43687734",
                            },
                            {
                                "type": "choice",
                                "value": "e8eba323-6a5a-4524-b4b2-54c87ccc47c9",
                            },
                        ],
                    },
                },
                {
                    "action": "add",
                    "details": {
                        "target": {"type": "variable", "value": "nutrition"},
                        "value": {"type": "constant", "value": 2},
                    },
                    "condition": {
                        "op": "is",
                        "vars": [
                            {
                                "type": "field",
                                "value": "b95e39c8-7076-4adc-82b7-743e43687734",
                            },
                            {
                                "type": "choice",
                                "value": "44f21c16-cc9b-4a67-b143-bb18e758260e",
                            },
                        ],
                    },
                },
                {
                    "action": "add",
                    "details": {
                        "target": {"type": "variable", "value": "nutrition"},
                        "value": {"type": "constant", "value": 2},
                    },
                    "condition": {
                        "op": "is",
                        "vars": [
                            {
                                "type": "field",
                                "value": "b95e39c8-7076-4adc-82b7-743e43687734",
                            },
                            {
                                "type": "choice",
                                "value": "bd0bf844-ed34-465a-ab9b-0bad90c6ebca",
                            },
                        ],
                    },
                },
            ],
        },
        {
            "type": "field",
            "ref": "fb8b5038-50a6-48ee-9b19-2ff8fcc3209f",
            "actions": [
                {
                    "action": "add",
                    "details": {
                        "target": {"type": "variable", "value": "social"},
                        "value": {"type": "constant", "value": 2},
                    },
                    "condition": {
                        "op": "is",
                        "vars": [
                            {
                                "type": "field",
                                "value": "fb8b5038-50a6-48ee-9b19-2ff8fcc3209f",
                            },
                            {
                                "type": "choice",
                                "value": "c9ccf549-5147-4667-a665-24fb3db2a9b0",
                            },
                        ],
                    },
                },
                {
                    "action": "add",
                    "details": {
                        "target": {"type": "variable", "value": "social"},
                        "value": {"type": "constant", "value": 4},
                    },
                    "condition": {
                        "op": "is",
                        "vars": [
                            {
                                "type": "field",
                                "value": "fb8b5038-50a6-48ee-9b19-2ff8fcc3209f",
                            },
                            {
                                "type": "choice",
                                "value": "d8ff3506-45bb-4685-8363-d23776fa0f8c",
                            },
                        ],
                    },
                },
                {
                    "action": "add",
                    "details": {
                        "target": {"type": "variable", "value": "social"},
                        "value": {"type": "constant", "value": 6},
                    },
                    "condition": {
                        "op": "is",
                        "vars": [
                            {
                                "type": "field",
                                "value": "fb8b5038-50a6-48ee-9b19-2ff8fcc3209f",
                            },
                            {
                                "type": "choice",
                                "value": "f6b73d89-c01a-4bec-8256-32391a29964d",
                            },
                        ],
                    },
                },
            ],
        },
        {
            "type": "field",
            "ref": "2dd55285-e399-482a-bcc2-6735c8b3f920",
            "actions": [
                {
                    "action": "add",
                    "details": {
                        "target": {"type": "variable", "value": "social"},
                        "value": {"type": "constant", "value": 2},
                    },
                    "condition": {
                        "op": "is",
                        "vars": [
                            {
                                "type": "field",
                                "value": "2dd55285-e399-482a-bcc2-6735c8b3f920",
                            },
                            {
                                "type": "choice",
                                "value": "f0373cc4-10d9-4dd5-8834-dcecd20a5687",
                            },
                        ],
                    },
                },
                {
                    "action": "add",
                    "details": {
                        "target": {"type": "variable", "value": "social"},
                        "value": {"type": "constant", "value": 4},
                    },
                    "condition": {
                        "op": "is",
                        "vars": [
                            {
                                "type": "field",
                                "value": "2dd55285-e399-482a-bcc2-6735c8b3f920",
                            },
                            {
                                "type": "choice",
                                "value": "8d5410f3-22c1-42bd-a311-a00cb19fd4c4",
                            },
                        ],
                    },
                },
                {
                    "action": "add",
                    "details": {
                        "target": {"type": "variable", "value": "social"},
                        "value": {"type": "constant", "value": 6},
                    },
                    "condition": {
                        "op": "is",
                        "vars": [
                            {
                                "type": "field",
                                "value": "2dd55285-e399-482a-bcc2-6735c8b3f920",
                            },
                            {
                                "type": "choice",
                                "value": "bae47e96-d239-4410-a3b1-5b348ad128a4",
                            },
                        ],
                    },
                },
            ],
        },
        {
            "type": "field",
            "ref": "37f43dc2-fbed-4007-be1e-d2e704ced61d",
            "actions": [
                {
                    "action": "add",
                    "details": {
                        "target": {"type": "variable", "value": "social"},
                        "value": {"type": "constant", "value": 2},
                    },
                    "condition": {
                        "op": "is",
                        "vars": [
                            {
                                "type": "field",
                                "value": "37f43dc2-fbed-4007-be1e-d2e704ced61d",
                            },
                            {
                                "type": "choice",
                                "value": "89b0cbef-1795-4c69-8c84-c6390fe43c77",
                            },
                        ],
                    },
                },
                {
                    "action": "add",
                    "details": {
                        "target": {"type": "variable", "value": "social"},
                        "value": {"type": "constant", "value": 4},
                    },
                    "condition": {
                        "op": "is",
                        "vars": [
                            {
                                "type": "field",
                                "value": "37f43dc2-fbed-4007-be1e-d2e704ced61d",
                            },
                            {
                                "type": "choice",
                                "value": "a5ee8528-db84-40dc-bd56-06da1bfce3e2",
                            },
                        ],
                    },
                },
            ],
        },
        {
            "type": "field",
            "ref": "a9978155-d312-456b-920a-4138ad0b7b1e",
            "actions": [
                {
                    "action": "add",
                    "details": {
                        "target": {"type": "variable", "value": "social"},
                        "value": {"type": "constant", "value": 2},
                    },
                    "condition": {
                        "op": "is",
                        "vars": [
                            {
                                "type": "field",
                                "value": "a9978155-d312-456b-920a-4138ad0b7b1e",
                            },
                            {
                                "type": "choice",
                                "value": "199a0157-68f7-4935-a34e-fd1c67e10c76",
                            },
                        ],
                    },
                },
                {
                    "action": "add",
                    "details": {
                        "target": {"type": "variable", "value": "social"},
                        "value": {"type": "constant", "value": 4},
                    },
                    "condition": {
                        "op": "is",
                        "vars": [
                            {
                                "type": "field",
                                "value": "a9978155-d312-456b-920a-4138ad0b7b1e",
                            },
                            {
                                "type": "choice",
                                "value": "01d585be-a9db-4e47-90c7-6c39919ed7d2",
                            },
                        ],
                    },
                },
            ],
        },
        {
            "type": "field",
            "ref": "b325df62-b561-464c-b846-5480803c4e8b",
            "actions": [
                {
                    "action": "add",
                    "details": {
                        "target": {"type": "variable", "value": "emotional"},
                        "value": {"type": "constant", "value": 2},
                    },
                    "condition": {
                        "op": "is",
                        "vars": [
                            {
                                "type": "field",
                                "value": "b325df62-b561-464c-b846-5480803c4e8b",
                            },
                            {
                                "type": "choice",
                                "value": "1badfccd-0012-440a-b1d2-4884f502f154",
                            },
                        ],
                    },
                },
                {
                    "action": "add",
                    "details": {
                        "target": {"type": "variable", "value": "emotional"},
                        "value": {"type": "constant", "value": 4},
                    },
                    "condition": {
                        "op": "is",
                        "vars": [
                            {
                                "type": "field",
                                "value": "b325df62-b561-464c-b846-5480803c4e8b",
                            },
                            {
                                "type": "choice",
                                "value": "43dce41d-adcc-4202-87e6-a5718302f8d0",
                            },
                        ],
                    },
                },
                {
                    "action": "add",
                    "details": {
                        "target": {"type": "variable", "value": "emotional"},
                        "value": {"type": "constant", "value": 6},
                    },
                    "condition": {
                        "op": "is",
                        "vars": [
                            {
                                "type": "field",
                                "value": "b325df62-b561-464c-b846-5480803c4e8b",
                            },
                            {
                                "type": "choice",
                                "value": "89dbf0c9-a433-4347-992a-8fd7fcb06da7",
                            },
                        ],
                    },
                },
            ],
        },
        {
            "type": "field",
            "ref": "d2744aa5-2f72-44a9-9ed3-5b2ecc4cb327",
            "actions": [
                {
                    "action": "add",
                    "details": {
                        "target": {"type": "variable", "value": "emotional"},
                        "value": {"type": "constant", "value": 2},
                    },
                    "condition": {
                        "op": "is",
                        "vars": [
                            {
                                "type": "field",
                                "value": "d2744aa5-2f72-44a9-9ed3-5b2ecc4cb327",
                            },
                            {
                                "type": "choice",
                                "value": "8f50a8c0-c9a9-4ebb-89bc-efcf5a66c864",
                            },
                        ],
                    },
                },
                {
                    "action": "add",
                    "details": {
                        "target": {"type": "variable", "value": "emotional"},
                        "value": {"type": "constant", "value": 4},
                    },
                    "condition": {
                        "op": "is",
                        "vars": [
                            {
                                "type": "field",
                                "value": "d2744aa5-2f72-44a9-9ed3-5b2ecc4cb327",
                            },
                            {
                                "type": "choice",
                                "value": "fddba980-223d-4134-bf98-9da86c5278d1",
                            },
                        ],
                    },
                },
                {
                    "action": "add",
                    "details": {
                        "target": {"type": "variable", "value": "emotional"},
                        "value": {"type": "constant", "value": 6},
                    },
                    "condition": {
                        "op": "is",
                        "vars": [
                            {
                                "type": "field",
                                "value": "d2744aa5-2f72-44a9-9ed3-5b2ecc4cb327",
                            },
                            {
                                "type": "choice",
                                "value": "ad2385a1-284a-4520-a697-e9e5563dbf4b",
                            },
                        ],
                    },
                },
            ],
        },
        {
            "type": "field",
            "ref": "f46a1097-7100-445f-865c-16a81552722c",
            "actions": [
                {
                    "action": "add",
                    "details": {
                        "target": {"type": "variable", "value": "emotional"},
                        "value": {"type": "constant", "value": 2},
                    },
                    "condition": {
                        "op": "is",
                        "vars": [
                            {
                                "type": "field",
                                "value": "f46a1097-7100-445f-865c-16a81552722c",
                            },
                            {
                                "type": "choice",
                                "value": "455d3e41-a59b-41a9-a990-f871978ace0b",
                            },
                        ],
                    },
                },
                {
                    "action": "add",
                    "details": {
                        "target": {"type": "variable", "value": "emotional"},
                        "value": {"type": "constant", "value": 4},
                    },
                    "condition": {
                        "op": "is",
                        "vars": [
                            {
                                "type": "field",
                                "value": "f46a1097-7100-445f-865c-16a81552722c",
                            },
                            {
                                "type": "choice",
                                "value": "49dba2e1-b74e-4740-9eed-7772695b7889",
                            },
                        ],
                    },
                },
                {
                    "action": "add",
                    "details": {
                        "target": {"type": "variable", "value": "emotional"},
                        "value": {"type": "constant", "value": 6},
                    },
                    "condition": {
                        "op": "is",
                        "vars": [
                            {
                                "type": "field",
                                "value": "f46a1097-7100-445f-865c-16a81552722c",
                            },
                            {
                                "type": "choice",
                                "value": "b6b5c8e9-9f11-4d5f-ab86-f6881aa4e409",
                            },
                        ],
                    },
                },
            ],
        },
        {
            "type": "field",
            "ref": "8f733407-bf55-4ec8-8101-2566221e9d94",
            "actions": [
                {
                    "action": "add",
                    "details": {
                        "target": {"type": "variable", "value": "emotional"},
                        "value": {"type": "constant", "value": 2},
                    },
                    "condition": {
                        "op": "is",
                        "vars": [
                            {
                                "type": "field",
                                "value": "8f733407-bf55-4ec8-8101-2566221e9d94",
                            },
                            {
                                "type": "choice",
                                "value": "8206dcb6-b259-4859-8b29-97cc44d6b30c",
                            },
                        ],
                    },
                },
                {
                    "action": "add",
                    "details": {
                        "target": {"type": "variable", "value": "emotional"},
                        "value": {"type": "constant", "value": 4},
                    },
                    "condition": {
                        "op": "is",
                        "vars": [
                            {
                                "type": "field",
                                "value": "8f733407-bf55-4ec8-8101-2566221e9d94",
                            },
                            {
                                "type": "choice",
                                "value": "8bcf8ebf-69f5-4d0a-96ff-b2171499040d",
                            },
                        ],
                    },
                },
            ],
        },
    ],
}
