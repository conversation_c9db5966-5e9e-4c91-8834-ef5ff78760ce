from unittest.mock import call
from fastapi.testclient import TestClient
from medo_api.main import app


def test_token_doesnt_exist():
    with TestClient(app) as client:
        response = client.get(
            "/profile",
            headers={"Authorization": "Bearer foo"},
        )
        assert response.status_code == 401


def test_authorized(client, mocker, recreate_db):
    """
    Test an authorised route which returns the user's profile.
    Will create the user if it doesn't exist in the DB
    """

    mock_verify = mocker.patch("medo_api.routers.auth.auth.verify_id_token")
    mock_verify.return_value = {
        "name": "<PERSON>",
        "email": "<EMAIL>",
        "uid": "firebase-a556",
    }
    token = "mock-af45"

    response = client.get(
        "/profile",
        headers={"Authorization": f"Bearer {token}"},
    )
    assert response.status_code == 200
    assert response.json() == {
        "name": "<PERSON>",
        "email": "<EMAIL>",
        "dob": None,
        "gender": None,
        "profile_bio_data": None,
        "user_attributes": None,
    }

    # auth.verify has been called with the given token
    assert mock_verify.call_count == 1
    mock_verify.assert_has_calls(
        [
            call(token),
        ]
    )
