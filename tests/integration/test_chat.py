from unittest.mock import AsyncMock, call

from medo_api.db.models import BinaryData


def test_chat_question(mocker, client, recreate_db, auth_token):
    session = recreate_db

    mock_processor_instance = AsyncMock()
    mock_processor_instance.execute.return_value = "The RAG answer"
    mock_processor = mocker.patch("medo_api.routers.chat_router.RAGProcessor")
    mock_processor.return_value = mock_processor_instance

    session.add(BinaryData(name="medo_0.1", contents=b"0"))
    session.commit()

    response = client.post(
        "/ask_question",
        headers={"Authorization": f"Bearer {auth_token}"},
        json={"question": "What is my purpose?"},
    )
    assert response.status_code == 200
    assert response.json() == {"answer": "The RAG answer"}

    # assert mock_execute.call_count == 1
    mock_processor_instance.assert_has_calls(
        [
            call.execute(question="What is my purpose?"),
        ]
    )

    # The answer exists in the chat history
    response = client.get(
        "/chat_history",
        headers={"Authorization": f"Bearer {auth_token}"},
    )
    assert response.status_code == 200
    histories = response.json()
    assert len(histories) == 1
    assert histories[0]["question"] == "What is my purpose?"
    assert histories[0]["answer"] == "The RAG answer"
