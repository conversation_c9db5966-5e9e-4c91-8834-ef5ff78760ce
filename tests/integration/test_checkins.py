from datetime import datetime

from medo_api.db.crud import create_checkin, get_user_by_firebase_uid
from medo_api.schemas.sense_score import Check<PERSON><PERSON><PERSON><PERSON><PERSON>


def test_checkins(client, auth_token, recreate_db):
    """
    Periodic check-in API
    """
    response = client.get(
        "/checkins",
        headers={"Authorization": f"Bearer {auth_token}"},
    )
    assert response.status_code == 200
    response_data = response.json()
    assert "checkins" in response_data
    assert isinstance(response_data["checkins"], list)
    assert "latestCheckinTimestamp" in response_data
    assert response_data["latestCheckinTimestamp"] is None

    # Pydantic validation
    response = client.post(
        "/checkins",
        headers={"Authorization": f"Bearer {auth_token}"},
        json={"foo": 1},
    )
    assert response.status_code == 422

    # Valid input 1
    response = client.post(
        "/checkins",
        headers={"Authorization": f"Bearer {auth_token}"},
        json={
            "nutrition": "I had seven croissants today",
            "emotionalWellbeing": 3,
            "socialConnection": 1,
        },
    )
    assert response.status_code == 200
    checkin_1 = response.json()
    assert (
        checkin_1.items()
        > {
            "nutrition": "I had seven croissants today",
            "emotionalWellbeing": 3,
            "socialConnection": 1,
        }.items()
    )

    # Valid input 2
    response = client.post(
        "/checkins",
        headers={"Authorization": f"Bearer {auth_token}"},
        json={
            "nutrition": "Had coffee",
            "emotionalWellbeing": 5,
            "socialConnection": 4,
        },
    )
    assert response.status_code == 200
    checkin_2 = response.json()

    # Get all
    response = client.get(
        "/checkins", headers={"Authorization": f"Bearer {auth_token}"}
    )
    assert response.status_code == 200
    response_data = response.json()
    assert "checkins" in response_data
    assert len(response_data["checkins"]) == 2
    assert response_data["checkins"] == [checkin_1, checkin_2]
    assert "latestCheckinTimestamp" in response_data
    assert response_data["latestCheckinTimestamp"] is not None

    # Valid input 3
    response = client.post(
        "/checkins",
        headers={"Authorization": f"Bearer {auth_token}"},
        json={
            "nutrition": "Had coffee",
        },
    )
    print(response.json())
    assert response.status_code == 200

    checkin_3 = response.json()

    # Get all
    response = client.get(
        "/checkins", headers={"Authorization": f"Bearer {auth_token}"}
    )
    assert response.status_code == 200
    response_data = response.json()
    assert "checkins" in response_data
    assert len(response_data["checkins"]) == 3
    assert response_data["checkins"] == [checkin_1, checkin_2, checkin_3]
    assert "latestCheckinTimestamp" in response_data
    assert response_data["latestCheckinTimestamp"] is not None


def test_checkins_from_date(client, auth_token, recreate_db):
    session = recreate_db

    # No checkins (also creates the user)
    response = client.get(
        "/checkins",
        headers={"Authorization": f"Bearer {auth_token}"},
    )
    assert response.status_code == 200
    response_data = response.json()
    assert "checkins" in response_data
    assert isinstance(response_data["checkins"], list)
    assert len(response_data["checkins"]) == 0
    assert "latestCheckinTimestamp" in response_data
    assert response_data["latestCheckinTimestamp"] is None

    user = get_user_by_firebase_uid(session=session, firebase_uid="firebase-a556")
    assert user

    checkin_1 = create_checkin(session=session, user=user, nutrition="One apple")
    checkin_2 = create_checkin(session=session, user=user, nutrition="Two bananas")
    checkin_3 = create_checkin(session=session, user=user, nutrition="Three cookies")
    iso_1 = "2023-06-28T14:21:00"
    iso_2 = "2023-07-01T08:11:00"
    iso_3 = "2023-07-01T16:17:00"
    checkin_1.created_at = datetime.fromisoformat(iso_1)
    checkin_2.created_at = datetime.fromisoformat(iso_2)
    checkin_3.created_at = datetime.fromisoformat(iso_3)
    session.commit()

    # All 3 checkins
    response = client.get(
        "/checkins",
        headers={"Authorization": f"Bearer {auth_token}"},
    )
    assert response.status_code == 200
    response_data = response.json()
    checkins = [CheckinReponse(**obj) for obj in response_data["checkins"]]
    assert [checkin.createdAt for checkin in checkins] == [iso_1, iso_2, iso_3]
    assert response_data["latestCheckinTimestamp"] == iso_3

    response = client.get(
        "/checkins?from=2023-07-01T00:00:00",
        headers={"Authorization": f"Bearer {auth_token}"},
    )
    assert response.status_code == 200
    response_data = response.json()
    checkins = [CheckinReponse(**obj) for obj in response_data["checkins"]]
    assert [checkin.createdAt for checkin in checkins] == [iso_2, iso_3]
    assert response_data["latestCheckinTimestamp"] == iso_3

    response = client.get(
        f"/checkins?from={iso_3}",
        headers={"Authorization": f"Bearer {auth_token}"},
    )
    assert response.status_code == 200
    response_data = response.json()
    checkins = [CheckinReponse(**obj) for obj in response_data["checkins"]]
    assert [checkin.createdAt for checkin in checkins] == [iso_3]
    assert response_data["latestCheckinTimestamp"] == iso_3
