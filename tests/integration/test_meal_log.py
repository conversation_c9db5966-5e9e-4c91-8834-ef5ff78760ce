from datetime import date, timedelta

from medo_api.db.crud import create_meal_log, create_user
from medo_api.db.models import MealLog  # Import the MealLog model


def test_get_meal_logs_with_date_filter(client, auth_token, recreate_db):
    session = recreate_db
    user = create_user(
        session,
        "firebase-a556",
        "<PERSON>",
        "<EMAIL>",
        date(1990, 1, 1),
        "other",
    )
    session.commit()

    today = date.today()
    yesterday = today - timedelta(days=1)

    create_meal_log(
        session,
        user,
        "Breakfast",
        "Toast",
        {"summary": "Good breakfast", "score": 4, "recommendations": "Add fruit"},
        False,
    )
    create_meal_log(
        session,
        user,
        "Lunch",
        "Salad",
        {"summary": "Healthy lunch", "score": 5, "recommendations": "Add protein"},
        False,
    )
    session.commit()

    # Debug: Check if meal logs are in the database
    meal_logs = session.query(MealLog).all()
    print(f"Number of meal logs in database: {len(meal_logs)}")
    for log in meal_logs:
        print(
            f"Meal log: {log.meal_type}, Date: {log.created_at}, User ID: {log.user_id}"
        )

    response = client.get(
        f"/meal_logs?log_date={today}",
        headers={"Authorization": f"Bearer {auth_token}"},
    )
    assert response.status_code == 200
    data = response.json()
    print(f"Response data: {data}")  # Debug: Print response data
    assert data["total"] == 2
    assert len(data["items"]) == 2

    response = client.get(
        f"/meal_logs?log_date={yesterday}",
        headers={"Authorization": f"Bearer {auth_token}"},
    )
    assert response.status_code == 200
    data = response.json()
    assert data["total"] == 0
    assert len(data["items"]) == 0


def test_get_meal_logs_pagination_with_date_filter(client, auth_token, recreate_db):
    session = recreate_db
    user = create_user(
        session,
        "firebase-a556",
        "Sarah",
        "<EMAIL>",
        date(1990, 1, 1),
        "other",
    )
    session.commit()

    today = date.today()

    for i in range(25):
        create_meal_log(
            session,
            user,
            f"Meal {i}",
            f"Description {i}",
            {"summary": f"Analysis {i}", "score": i % 5 + 1, "recommendations": "None"},
            False,
        )
    session.commit()

    # Debug: Check if meal logs are in the database
    meal_logs = session.query(MealLog).all()
    print(f"Number of meal logs in database: {len(meal_logs)}")
    for log in meal_logs:
        print(
            f"Meal log: {log.meal_type}, Date: {log.created_at}, User ID: {log.user_id}"
        )

    response = client.get(
        f"/meal_logs?log_date={today}&page=1&size=10",
        headers={"Authorization": f"Bearer {auth_token}"},
    )
    assert response.status_code == 200
    data = response.json()
    print(f"Response data: {data}")  # Debug: Print response data
    assert data["total"] == 25
    assert len(data["items"]) == 10
    assert data["page"] == 1
    assert data["pages"] == 3

    response = client.get(
        f"/meal_logs?log_date={today}&page=3&size=10",
        headers={"Authorization": f"Bearer {auth_token}"},
    )
    assert response.status_code == 200
    data = response.json()
    assert data["total"] == 25
    assert len(data["items"]) == 5
    assert data["page"] == 3
    assert data["pages"] == 3


def test_delete_meal_log(client, auth_token, recreate_db):
    session = recreate_db
    user = create_user(
        session,
        "firebase-a556",
        "Sarah",
        "<EMAIL>",
        date(1990, 1, 1),
        "other",
    )
    session.commit()

    # Create a meal log
    create_meal_log(
        session,
        user,
        "Breakfast",
        "Toast",
        {"summary": "Good breakfast", "score": 4, "recommendations": "Add fruit"},
        False,
    )
    session.commit()

    # Verify the meal log exists
    response = client.get(
        "/meal_logs",
        headers={"Authorization": f"Bearer {auth_token}"},
    )
    assert response.status_code == 200
    data = response.json()
    assert data["total"] == 1
    meal_log_id = data["items"][0]["id"]

    # Delete the meal log
    response = client.delete(
        f"/meal_logs/{meal_log_id}",
        headers={"Authorization": f"Bearer {auth_token}"},
    )
    assert response.status_code == 204

    # Verify the meal log is deleted
    response = client.get(
        "/meal_logs",
        headers={"Authorization": f"Bearer {auth_token}"},
    )
    assert response.status_code == 200
    data = response.json()
    assert data["total"] == 0

    # Try to delete a non-existent meal log
    response = client.delete(
        "/meal_logs/999999",
        headers={"Authorization": f"Bearer {auth_token}"},
    )
    assert response.status_code == 404

    # Try to delete an already deleted meal log
    response = client.delete(
        f"/meal_logs/{meal_log_id}",
        headers={"Authorization": f"Bearer {auth_token}"},
    )
    assert response.status_code == 404
