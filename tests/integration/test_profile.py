def test_profile_crud(client, recreate_db, auth_token):
    # Initial profile
    response = client.get(
        "/profile",
        headers={"Authorization": f"Bearer {auth_token}"},
    )
    assert response.status_code == 200
    assert response.json() == {
        "dob": None,
        "email": "<EMAIL>",
        "gender": None,
        "name": "<PERSON>",
        "profile_bio_data": None,
        "user_attributes": None,
    }

    # Create/Update profile
    response = client.patch(
        "/profile",
        headers={"Authorization": f"Bearer {auth_token}"},
        json={
            "email": "<EMAIL>",
            "dob": "1990-01-01",
        },
    )
    assert response.status_code == 200
    profile_data = response.json()
    assert profile_data == {
        "name": "<PERSON>",
        "dob": "1990-01-01",
        "gender": None,
        "email": "<EMAIL>",
        "profile_bio_data": None,
        "user_attributes": None,
    }

    # Get profile
    response = client.get(
        "/profile",
        headers={"Authorization": f"Bearer {auth_token}"},
    )
    assert response.status_code == 200
    assert response.json() == profile_data

    # Update profile
    response = client.patch(
        "/profile",
        headers={"Authorization": f"Bearer {auth_token}"},
        json={"name": "<PERSON> <PERSON>", "dob": "1985-12-31", "gender": "male"},
    )
    assert response.status_code == 200
    updated_profile = response.json()
    assert updated_profile == {
        "name": "John Smith",
        "dob": "1985-12-31",
        "gender": "male",
        "email": "<EMAIL>",
        "profile_bio_data": None,
        "user_attributes": None,
    }

    # Verify updated profile
    response = client.get(
        "/profile",
        headers={"Authorization": f"Bearer {auth_token}"},
    )
    assert response.status_code == 200
    assert response.json() == updated_profile


def test_profile_dob_formats(client, recreate_db, auth_token):
    # Test ISO 8601 date format
    response = client.patch(
        "/profile",
        headers={"Authorization": f"Bearer {auth_token}"},
        json={"dob": "1990-10-30"},
    )
    assert response.status_code == 200
    assert response.json()["dob"] == "1990-10-30"

    # Test ISO 8601 date format
    response = client.patch(
        "/profile",
        headers={"Authorization": f"Bearer {auth_token}"},
        json={"dob": "1990-30-10"},
    )
    assert response.status_code == 422

    # Test invalid date format
    response = client.patch(
        "/profile",
        headers={"Authorization": f"Bearer {auth_token}"},
        json={"dob": "01/01/1990"},
    )
    assert response.status_code == 422  # Unprocessable Entity


def test_profile_invalid_dob(client, recreate_db, auth_token):
    response = client.patch(
        "/profile",
        headers={"Authorization": f"Bearer {auth_token}"},
        json={"dob": "invalid-date"},
    )
    assert response.status_code == 422  # Unprocessable Entity


def test_not_authorized(client, recreate_db):
    # Initial profile
    response = client.get(
        "/profile",
    )
    assert response.status_code == 401
    assert response.json() == {"detail": "Not authenticated"}


def test_profile_bio_data_and_user_attributes(client, recreate_db, auth_token):
    # Update profile with bio data and qa pairs
    response = client.patch(
        "/profile",
        headers={"Authorization": f"Bearer {auth_token}"},
        json={
            "profile_bio_data": "Test bio data",
            "user_attributes": [
                {"question": "question1", "answer": "answer1"},
                {"question": "question2", "answer": "answer2"},
            ],
        },
    )
    assert response.status_code == 200
    updated_profile = response.json()
    assert updated_profile["profile_bio_data"] == "Test bio data"
    assert updated_profile["user_attributes"] == [
        {"question": "question1", "answer": "answer1"},
        {"question": "question2", "answer": "answer2"},
    ]

    # Verify updated profile
    response = client.get(
        "/profile",
        headers={"Authorization": f"Bearer {auth_token}"},
    )
    assert response.status_code == 200
    assert response.json() == updated_profile


def test_profile_partial_update(client, recreate_db, auth_token):
    # Initial update
    client.patch(
        "/profile",
        headers={"Authorization": f"Bearer {auth_token}"},
        json={"name": "John Doe", "email": "<EMAIL>"},
    )

    # Partial update
    response = client.patch(
        "/profile",
        headers={"Authorization": f"Bearer {auth_token}"},
        json={"name": "Jane Doe"},
    )
    assert response.status_code == 200
    updated_profile = response.json()
    assert updated_profile["name"] == "Jane Doe"
    assert updated_profile["email"] == "<EMAIL>"


def test_profile_invalid_email(client, recreate_db, auth_token):
    response = client.patch(
        "/profile",
        headers={"Authorization": f"Bearer {auth_token}"},
        json={"email": "invalid-email"},
    )
    assert response.status_code == 422
    assert "value is not a valid email address" in response.json()["detail"][0]["msg"]


def test_profile_generate_bio(client, recreate_db, auth_token):
    # Update profile with qa pairs
    client.patch(
        "/profile",
        headers={"Authorization": f"Bearer {auth_token}"},
        json={
            "user_attributes": [
                {"question": "question1", "answer": "answer1"},
                {"question": "question2", "answer": "answer2"},
            ]
        },
    )

    # Generate bio
    response = client.post(
        "/profile/bio",
        headers={"Authorization": f"Bearer {auth_token}"},
        json={"use_existing_user_attributes": True},
    )
    assert response.status_code == 200
    assert isinstance(response.json(), str)

    # Verify bio is updated in profile
    profile_response = client.get(
        "/profile",
        headers={"Authorization": f"Bearer {auth_token}"},
    )
    assert profile_response.status_code == 200
    assert profile_response.json()["profile_bio_data"] == response.json()


def test_profile_generate_bio_with_new_user_attributes(client, recreate_db, auth_token):
    response = client.post(
        "/profile/bio",
        headers={"Authorization": f"Bearer {auth_token}"},
        json={
            "use_existing_user_attributes": False,
            "user_attributes": [{"question": "new_question", "answer": "new_answer"}],
        },
    )
    assert response.status_code == 200
    assert isinstance(response.json(), str)


def test_profile_generate_bio_invalid_request(client, recreate_db, auth_token):
    response = client.post(
        "/profile/bio",
        headers={"Authorization": f"Bearer {auth_token}"},
        json={"use_existing_user_attributes": False},
    )
    assert response.status_code == 422
    assert "User attributes are required" in response.json()["detail"]
