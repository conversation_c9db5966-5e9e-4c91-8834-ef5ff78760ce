import pytest
from unittest.mock import patch, AsyncMock

from medo_api.db.models import <PERSON>llar, BinaryData


@pytest.fixture
def mock_rag_processor():
    with patch("medo_api.ai.recommendations.RAGProcessor") as mock:
        mock.return_value.execute = AsyncMock(
            side_effect=lambda question: (
                "Strengths: Mock strength"
                if "strengths" in question.lower()
                else "Areas for Improvement: Mock area for improvement"
            )
        )
        yield mock


@pytest.fixture
def recommendation_index(recreate_db):
    session = recreate_db
    binary_data = BinaryData(name="medo_0.1", contents=b"mock index data")
    session.add(binary_data)
    session.commit()
    yield
    session.query(BinaryData).filter_by(name="medo_0.1").delete()
    session.commit()


def assert_valid_recommendation(recommendation):
    assert "strengths" in recommendation, "Missing strengths"
    assert "areasForImprovement" in recommendation, "Missing areas for improvement"
    assert recommendation["strengths"] == "Strengths: Mock strength"
    assert (
        recommendation["areasForImprovement"]
        == "Areas for Improvement: Mock area for improvement"
    )


def test_pillar_recommendations(
    client,
    auth_token,
    recommendation_index,
    mock_rag_processor,
):
    response = client.get(
        "/recommendations", headers={"Authorization": f"Bearer {auth_token}"}
    )

    assert (
        response.status_code == 200
    ), f"Failed to get recommendations: {response.json()}"
    recommendations = response.json()

    for pillar in list(Pillar):
        assert (
            pillar.value in recommendations
        ), f"Missing {pillar.value} in recommendations"
        assert_valid_recommendation(recommendations[pillar.value])
