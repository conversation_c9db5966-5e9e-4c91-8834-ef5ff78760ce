from requests.exceptions import HTT<PERSON><PERSON>rror
from .resources.typeform_responses import (
    responses_with_scores,
    invalid_result_no_items,
    invalid_result_bad_item,
)
from .resources.typeform_structure import form_structure


def test_invalid_no_items_typeform_survey(client, mocker, recreate_db, auth_token):
    mock_typeform_responses = mocker.patch(
        "medo_api.routers.sense_score_router.get_typeform_responses",
    )
    mock_typeform_responses.return_value = invalid_result_no_items

    # Typeform form not found
    response = client.post(
        "/typeform_survey",
        headers={"Authorization": f"Bearer {auth_token}"},
        json={"formId": "x1", "responseId": "123"},
    )
    assert response.status_code == 404


def test_invalid_bad_item_typeform_survey(client, mocker, recreate_db, auth_token):
    mock_typeform_responses = mocker.patch(
        "medo_api.routers.sense_score_router.get_typeform_responses",
    )
    mock_typeform_responses.return_value = invalid_result_bad_item

    # Typeform form not found
    response = client.post(
        "/typeform_survey",
        headers={"Authorization": f"Bearer {auth_token}"},
        json={"formId": "x1", "responseId": "123"},
    )
    assert response.status_code == 404


def test_no_survey(client, mocker, recreate_db, auth_token):
    def typeform_responses(form_id: str):
        raise HTTPError("Form not found")

    mocker.patch(
        "medo_api.routers.sense_score_router.get_typeform_responses",
        side_effect=typeform_responses,
    )

    # No SENSE score
    response = client.get(
        "/sense_score", headers={"Authorization": f"Bearer {auth_token}"}
    )
    assert response.status_code == 404
    assert response.json() == {"detail": "Initial pillars not found"}

    # No initial pillars
    response = client.get(
        "/initial_pillars",
        headers={"Authorization": f"Bearer {auth_token}"},
    )
    assert response.status_code == 404
    assert response.json() == {"detail": "Not Found"}

    # Typeform form not found
    response = client.post(
        "/typeform_survey",
        headers={"Authorization": f"Bearer {auth_token}"},
        json={"formId": "x1", "responseId": "123"},
    )
    assert response.status_code == 404

    # Typeform response not found
    response = client.post(
        "/typeform_survey",
        headers={"Authorization": f"Bearer {auth_token}"},
        json={"formId": "jSCjGrU3", "responseId": "123"},
    )
    assert response.status_code == 404


def test_typeform_survey(client, mocker, recreate_db, auth_token):

    def typeform_responses(form_id: str):
        if form_id == "jbJilFOr":
            return responses_with_scores
        else:
            raise HTTPError("Form not found")

    def typeform_structure(form_id: str):
        if form_id == "jbJilFOr":
            return form_structure
        else:
            raise HTTPError("Form not found")

    mocker.patch(
        "medo_api.routers.sense_score_router.get_typeform_responses",
        side_effect=typeform_responses,
    )

    mocker.patch(
        "medo_api.routers.sense_score_router.fetch_typeform_structure",
        side_effect=typeform_structure,
    )

    # Typeform Response found - return Initial pillars & SENSE score
    response = client.post(
        "/typeform_survey",
        headers={"Authorization": f"Bearer {auth_token}"},
        json={
            "formId": "jbJilFOr",
            "responseId": "y7xgnz5javb3qy7xgc8xvvnjzhk7y6ph",
        },
    )
    assert response.status_code == 200
    result = response.json()
    assert result == {
        "pillars": {
            "sleep": 45,
            "exercise": 85,
            "nutrition": 85,
            "socialConnection": 80,
            "emotionalWellbeing": 70,
        },
        "senseScore": 73,
    }

    # Can't complete another survey. Don't even fetch a typeform response
    response = client.post(
        "/typeform_survey",
        headers={"Authorization": f"Bearer {auth_token}"},
        json={
            "formId": "foo",
            "responseId": "123",
        },
    )
    assert response.status_code == 400
    assert response.json() == {"detail": "User has already completed a survey"}

    # Delete initial pillars
    response = client.delete(
        "/initial_pillars",
        headers={"Authorization": f"Bearer {auth_token}"},
    )
    assert response.status_code == 200
    assert response.json() == "deleted"

    response = client.get(
        "/initial_pillars",
        headers={"Authorization": f"Bearer {auth_token}"},
    )
    assert response.status_code == 404
    assert response.json() == {"detail": "Not Found"}
